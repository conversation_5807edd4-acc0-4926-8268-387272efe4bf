import { isValidUsername, isExternal } from '@/utils/validate';

describe('Utils:validate', () => {
    it('isValidUsername', () => {
        expect(isValidUsername('admin')).toBe(true);
        expect(isValidUsername('editor')).toBe(true);
        expect(isValidUsername('xxxx')).toBe(false);
    });

    it('isExternal', () => {
        expect(isExternal('https://www.armour.com/')).toBe(true);
        expect(isExternal('mailto:<EMAIL>')).toBe(true);
        expect(isExternal('123aBC')).toBe(false);
    });
});
