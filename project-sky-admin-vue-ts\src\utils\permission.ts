/**
 * 权限控制工具函数
 */
import { getUserInfo } from '@/utils/cookies'
import store from '@/store'

/**
 * 检查当前用户是否为管理员（商户ID为1）
 * @returns {boolean} 是否为管理员
 */
export function isAdmin(): boolean {
  const currentUserId = getCurrentUserId()
  return currentUserId === 1
}

/**
 * 获取当前用户ID
 * @returns {number | null} 用户ID
 */
export function getCurrentUserId(): number | null {
  // 方法1: 从 store 获取
  if (store.state.user.userInfo && store.state.user.userInfo.id) {
    return store.state.user.userInfo.id
  }

  // 方法2: 从 cookies 获取
  const userInfo = getUserInfo()
  if (userInfo) {
    try {
      const parsedUserInfo = JSON.parse(userInfo)
      if (parsedUserInfo && parsedUserInfo.id) {
        return parsedUserInfo.id
      }
    } catch (e) {
      console.error('解析用户信息失败:', e)
    }
  }

  // 方法3: 从 cookies 直接获取（备用方案）
  try {
    const userInfoFromCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('user_info='))

    if (userInfoFromCookie) {
      const userInfoValue = decodeURIComponent(userInfoFromCookie.split('=')[1])
      const parsedUserInfo = JSON.parse(userInfoValue)
      if (parsedUserInfo && parsedUserInfo.id) {
        return parsedUserInfo.id
      }
    }
  } catch (e) {
    console.error('从cookie解析用户信息失败:', e)
  }

  return null
}
