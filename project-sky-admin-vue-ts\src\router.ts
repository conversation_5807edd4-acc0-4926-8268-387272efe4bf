import Vue from "vue";
import Router from "vue-router";
import Layout from "@/layout/index.vue";
import { isAdmin } from "@/utils/permission";
import { UserModule } from "@/store/modules/user";
import { getUserInfo } from "@/utils/cookies";

Vue.use(Router);

const router = new Router({
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition;
    }
    return { x: 0, y: 0 };
  },
  base: process.env.BASE_URL,
  routes: [
    {
      path: "/login",
      component: () =>
        import(/* webpackChunkName: "login" */ "@/views/login/index.vue"),
      meta: { title: "后台管理", hidden: true, notNeedAuth: true }
    },
    {
      path: "/404",
      component: () => import(/* webpackChunkName: "404" */ "@/views/404.vue"),
      meta: { title: "后台管理", hidden: true, notNeedAuth: true }
    },
    {
      path: "/",
      component: Layout,
      redirect: "/dashboard",
      children: [
        {
          path: "dashboard",
          component: () =>
            import(/* webpackChunkName: "dashboard" */ "@/views/dashboard/index.vue"),
          name: "Dashboard",
          meta: {
            title: "工作台",
            icon: "dashboard",
            affix: true,
            requiresAdminId: true
          }
        },
        {
          path: "/statistics",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/statistics/index.vue"),
          meta: {
            title: "数据统计",
            icon: "icon-statistics",
            requiresAdminId: true
          }
        },
        {
          path: "order",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/orderDetails/index.vue"),
          meta: {
            title: "订单管理",
            icon: "icon-order",
            requiresAdminId: true
          }
        },
        {
          path: "dish",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/dish/index.vue"),
          meta: {
            title: "商品管理",
            icon: "icon-dish"
          }
        },
        {
          path: "/dish/add",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/dish/addDishtype.vue"),
          meta: {
            title: "添加商品",
            hidden: true
          }
        },

        {
          path: "category",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/category/index.vue"),
          meta: {
            title: "分类管理",
            icon: "icon-category"
          }
        },
        {
          path: "employee",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/employee/index.vue"),
          meta: {
            title: "商户管理",
            icon: "icon-employee",
            requiresAdminId: true
          }
        },

        {
          path: "/employee/add",
          component: () =>
            import(/* webpackChunkName: "dashboard" */ "@/views/employee/addEmployee.vue"),
          meta: {
            title: "添加商户",
            hidden: true
          }
        },

        {
          path: "/setmeal/add",
          component: () =>
            import(/* webpackChunkName: "shopTable" */ "@/views/setmeal/addSetmeal.vue"),
          meta: {
            title: "添加套餐",
            hidden: true
          }
        }
      ]
    },
    {
      path: "*",
      redirect: "/404",
      meta: { hidden: true }
    }
  ]
});

// 路由守卫：检查权限
router.beforeEach(async (to, from, next) => {
  // 检查路由是否需要管理员权限
  if (to.meta && to.meta.requiresAdminId) {
    // 确保用户信息已加载
    await ensureUserInfoLoaded()

    // 只有商户ID为1的用户才能访问
    if (!isAdmin()) {
      console.log('权限不足，重定向到商品管理页面')
      // 重定向到商品管理页面（或其他允许访问的页面）
      next('/dish')
      return
    }
  }

  next()
})

// 确保用户信息已加载的辅助函数
async function ensureUserInfoLoaded() {
  // 如果 store 中已有用户信息，直接返回
  if (UserModule.userInfo && Object.keys(UserModule.userInfo).length > 0) {
    return
  }

  // 尝试从 cookies 恢复用户信息到 store
  try {
    const userInfo = getUserInfo()
    if (userInfo) {
      const parsedUserInfo = JSON.parse(userInfo)
      if (parsedUserInfo && Object.keys(parsedUserInfo).length > 0) {
        // 手动设置到 store 中
        UserModule.SetUserInfo(parsedUserInfo)
      }
    }
  } catch (e) {
    console.error('恢复用户信息失败:', e)
  }
}

export default router;
