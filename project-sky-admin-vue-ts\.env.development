# Base api
VUE_APP_BASE_API = '/api'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

NODE_ENV = 'development'
VUE_APP_NODE_ENV = 'dev'

//后端服务的地址
VUE_APP_URL = 'http://localhost:8080/admin'

VUE_APP_SOCKET_URL = 'http://localhost:8652/admin'
//VUE_APP_SOCKET_URL = 'ws://http://23ce4dbd.cpolar.cn/ws/'


VUE_CLI_BABEL_TRANSPILE_MODULES = true
# 删除权限 true/有
VUE_APP_DELETE_PERMISSIONS = true
