{"name": "vue-typescript-admin-template", "version": "0.1.0", "private": true, "author": "<PERSON><PERSON> <<EMAIL>>", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:uat": "vue-cli-service build --mode production.uat", "lint": "vue-cli-service lint", "svg": "vsvg -s ./src/icons/svg -t ./src/icons/components --ext ts --es6", "test:e2e": "vue-cli-service test:e2e", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@types/echarts": "^4.4.6", "@types/webpack": "^4.41.12", "area-data": "^5.0.6", "axios": "^0.19.0", "echarts": "^5.3.2", "element-ui": "^2.12.0", "js-cookie": "^2.2.1", "md5": "^2.3.0", "moment": "^2.24.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^3.0.0", "register-service-worker": "^1.6.2", "vue": "^2.6.10", "vue-area-linkage": "^5.1.0", "vue-class-component": "^7.1.0", "vue-property-decorator": "^8.2.2", "vue-router": "^3.1.2", "vue-svgicon": "^3.2.6", "vuex": "^3.1.1", "vuex-class": "^0.3.2", "vuex-module-decorators": "^0.10.1", "vuex-persistedstate": "^2.7.0"}, "devDependencies": {"@types/jest": "^24.0.18", "@types/js-cookie": "^2.2.2", "@types/nprogress": "^0.2.0", "@types/webpack-env": "^1.14.0", "@vue/cli-plugin-babel": "^3.11.0", "@vue/cli-plugin-e2e-cypress": "^3.11.0", "@vue/cli-plugin-eslint": "^3.11.0", "@vue/cli-plugin-pwa": "^3.11.0", "@vue/cli-plugin-typescript": "^3.11.0", "@vue/cli-plugin-unit-jest": "^3.11.0", "@vue/cli-service": "^3.11.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/eslint-config-typescript": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.3", "eslint": "^6.2.2", "eslint-plugin-vue": "^5.2.3", "jest": "^24.9.0", "sass": "^1.22.10", "sass-loader": "^7.3.1", "style-resources-loader": "^1.2.1", "ts-jest": "^24.0.2", "typescript": "^4.9.5", "vue-cli-plugin-element": "^1.0.1", "vue-cli-plugin-style-resources-loader": "^0.1.3", "vue-template-compiler": "^2.6.10", "webpack": "^4.39.3"}}