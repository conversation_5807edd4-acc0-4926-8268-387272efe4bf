# 商户权限控制实现说明

## 功能概述
实现了基于商户ID的权限控制，只有商户ID为1的用户才能访问以下功能模块：
- 工作台 (Dashboard)
- 数据统计 (Statistics)
- 订单管理 (Order Management)
- 商户管理 (Employee Management)

## 实现方案

### 1. 路由配置修改 (`src/router.ts`)
- 在需要权限控制的路由的 `meta` 字段中添加了 `requiresAdminId: true` 标识
- 添加了路由守卫 `router.beforeEach`，检查访问权限
- 如果非管理员用户尝试访问受限页面，会自动重定向到商品管理页面

### 2. 侧边栏菜单过滤 (`src/layout/components/Sidebar/index.vue`)
- 在侧边栏组件的 `routes` 计算属性中添加了权限过滤逻辑
- 只有管理员用户才能在侧边栏中看到受限的菜单项

### 3. 权限工具函数 (`src/utils/permission.ts`)
- 创建了 `isAdmin()` 函数用于检查当前用户是否为管理员
- 创建了 `getCurrentUserId()` 函数用于获取当前用户ID
- 支持从 Vuex store 和 cookies 两种方式获取用户信息

## 权限检查逻辑
1. 优先从 Vuex store 中获取用户信息
2. 如果 store 中没有，则从 cookies 中获取 `user_info`
3. 判断用户ID是否等于1，只有ID为1的用户被认为是管理员

## 受限页面列表
- `/dashboard` - 工作台
- `/statistics` - 数据统计  
- `/order` - 订单管理
- `/employee` - 商户管理

## 非受限页面
其他用户仍可正常访问：
- `/dish` - 商品管理
- `/category` - 分类管理
- `/setmeal/add` - 添加套餐
- 等其他页面

## 测试方法
1. 使用商户ID为1的账号登录，应该能看到所有菜单项
2. 使用其他商户ID的账号登录，应该看不到工作台、数据统计、订单管理、商户管理菜单
3. 直接在浏览器地址栏输入受限页面URL，非管理员用户会被重定向到商品管理页面

## 注意事项
- 权限控制基于前端实现，后端API也应该有相应的权限验证
- 用户信息的获取依赖于登录时保存的数据结构
- 如果需要修改管理员ID，可以在 `src/utils/permission.ts` 中修改判断条件
