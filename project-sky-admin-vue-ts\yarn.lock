# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.0.0-beta.35", "@babel/code-frame@^7.5.5":
  "integrity" "sha1-vAeC9tafe31JUxIZaZuYj2aaj50="
  "resolved" "https://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.5.5.tgz?cache=0&sync_timestamp=1563398593063&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.5.5.tgz"
  "version" "7.5.5"
  dependencies:
    "@babel/highlight" "^7.0.0"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.1.0":
  "integrity" "sha1-N+hkUyIAy2tQ7ppARfX4F4QBZqs="
  "resolved" "https://registry.npm.taobao.org/@babel/core/download/@babel/core-7.7.4.tgz?cache=0&sync_timestamp=1574465962805&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.7.4"
    "@babel/helpers" "^7.7.4"
    "@babel/parser" "^7.7.4"
    "@babel/template" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "json5" "^2.1.0"
    "lodash" "^4.17.13"
    "resolve" "^1.3.2"
    "semver" "^5.4.1"
    "source-map" "^0.5.0"

"@babel/generator@^7.4.0", "@babel/generator@^7.7.4":
  "integrity" "sha1-22UeKEDKmqZvMn3OwdxfX6lhE2k="
  "resolved" "https://registry.npm.taobao.org/@babel/generator/download/@babel/generator-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"
    "jsesc" "^2.5.1"
    "lodash" "^4.17.13"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.7.4":
  "integrity" "sha1-uz+vHnS3S9VH6Gfkj1UfprCYts4="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.7.4.tgz?cache=0&sync_timestamp=1574466122659&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-annotate-as-pure%2Fdownload%2F%40babel%2Fhelper-annotate-as-pure-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.7.4":
  "integrity" "sha1-X3PysoWA4iS1ub0DFGpAFdYhf18="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-call-delegate@^7.7.4":
  "integrity" "sha1-YhuD5ZZyK1DABm+dw30yMuRhuAE="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-call-delegate/download/@babel/helper-call-delegate-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-hoist-variables" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-create-class-features-plugin@^7.7.4":
  "integrity" "sha1-/OYJOf1QYYYQlCMgqNlRs7Y52i0="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.7.4.tgz?cache=0&sync_timestamp=1574466627752&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-create-class-features-plugin%2Fdownload%2F%40babel%2Fhelper-create-class-features-plugin-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-function-name" "^7.7.4"
    "@babel/helper-member-expression-to-functions" "^7.7.4"
    "@babel/helper-optimise-call-expression" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.7.4"
    "@babel/helper-split-export-declaration" "^7.7.4"

"@babel/helper-create-regexp-features-plugin@^7.7.4":
  "integrity" "sha1-bVdiNZ/TT02hUA5M/5lVtSmar1k="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-create-regexp-features-plugin%2Fdownload%2F%40babel%2Fhelper-create-regexp-features-plugin-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-regex" "^7.4.4"
    "regexpu-core" "^4.6.0"

"@babel/helper-define-map@^7.7.4":
  "integrity" "sha1-KEG/kuuL2ckGhRVG/mudReFi8XY="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-define-map/download/@babel/helper-define-map-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-define-map%2Fdownload%2F%40babel%2Fhelper-define-map-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-function-name" "^7.7.4"
    "@babel/types" "^7.7.4"
    "lodash" "^4.17.13"

"@babel/helper-explode-assignable-expression@^7.7.4":
  "integrity" "sha1-+nAIeOAI2F3FG6Q+n7g1zd/gXIQ="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-function-name@^7.7.4":
  "integrity" "sha1-q24EHnE11DbY8KPsoV3ltno0Gi4="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-function-name/download/@babel/helper-function-name-7.7.4.tgz?cache=0&sync_timestamp=1574465741457&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-get-function-arity" "^7.7.4"
    "@babel/template" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-get-function-arity@^7.7.4":
  "integrity" "sha1-y0Y0jS+ICOYy8KsEgXITDmNgBfA="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.7.4.tgz?cache=0&sync_timestamp=1574465736980&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-get-function-arity%2Fdownload%2F%40babel%2Fhelper-get-function-arity-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-hoist-variables@^7.7.4":
  "integrity" "sha1-YSOE49gj/fqvn84xVQ/l1NsPPRI="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-hoist-variables%2Fdownload%2F%40babel%2Fhelper-hoist-variables-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-member-expression-to-functions@^7.7.4":
  "integrity" "sha1-NWQ44lad9zIagyZkTUt5DSEiy3Q="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-member-expression-to-functions%2Fdownload%2F%40babel%2Fhelper-member-expression-to-functions-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.7.4":
  "integrity" "sha1-5aklKfiIi/MZpjdqv70c68SRrZE="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-module-imports/download/@babel/helper-module-imports-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-module-transforms@^7.7.4":
  "integrity" "sha1-jXzbHh+Oo9jDiwZzRZJKxPjgh5o="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-module-imports" "^7.7.4"
    "@babel/helper-simple-access" "^7.7.4"
    "@babel/helper-split-export-declaration" "^7.7.4"
    "@babel/template" "^7.7.4"
    "@babel/types" "^7.7.4"
    "lodash" "^4.17.13"

"@babel/helper-optimise-call-expression@^7.7.4":
  "integrity" "sha1-A0rzE3DSmVJCqk30AsO3eUstzfI="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-optimise-call-expression%2Fdownload%2F%40babel%2Fhelper-optimise-call-expression-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-plugin-utils@^7.0.0":
  "integrity" "sha1-u7P77phmHFaQNCN8wDlnupm08lA="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.0.0.tgz"
  "version" "7.0.0"

"@babel/helper-regex@^7.0.0", "@babel/helper-regex@^7.4.4":
  "integrity" "sha1-CqaCT3EAouDonBUnwjk2wVLKs1E="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-regex/download/@babel/helper-regex-7.5.5.tgz"
  "version" "7.5.5"
  dependencies:
    "lodash" "^4.17.13"

"@babel/helper-remap-async-to-generator@^7.7.4":
  "integrity" "sha1-xowkBzUNmvDgYe1nJq+0//FtAjQ="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.7.4"
    "@babel/helper-wrap-function" "^7.7.4"
    "@babel/template" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-replace-supers@^7.7.4":
  "integrity" "sha1-PIgaamp1cSdactguYQcSbsnizdI="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.7.4"
    "@babel/helper-optimise-call-expression" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-simple-access@^7.7.4":
  "integrity" "sha1-oWmgrbG19BjPwZ8iWGsuv1ipopQ="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-simple-access/download/@babel/helper-simple-access-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-simple-access%2Fdownload%2F%40babel%2Fhelper-simple-access-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/template" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-split-export-declaration@^7.7.4":
  "integrity" "sha1-Vykq9gRDxKNiLPdAQN3Cjmgzb9g="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-wrap-function@^7.7.4":
  "integrity" "sha1-N6t/7VFQ4i2dcmboMAcsDN2Lqs4="
  "resolved" "https://registry.npm.taobao.org/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-function-name" "^7.7.4"
    "@babel/template" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helpers@^7.7.4":
  "integrity" "sha1-YsIVuebHEtrcFamg3Kt2ySqUAwI="
  "resolved" "https://registry.npm.taobao.org/@babel/helpers/download/@babel/helpers-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/template" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/highlight@^7.0.0":
  "integrity" "sha1-VtETEr2SSPphlZHQJHK+boyzJUA="
  "resolved" "https://registry.npm.taobao.org/@babel/highlight/download/@babel/highlight-7.5.0.tgz"
  "version" "7.5.0"
  dependencies:
    "chalk" "^2.0.0"
    "esutils" "^2.0.2"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.1.0", "@babel/parser@^7.4.3", "@babel/parser@^7.7.4":
  "integrity" "sha1-dastcRDCzy+pSZWa+wX6NG0iMbs="
  "resolved" "https://registry.npm.taobao.org/@babel/parser/download/@babel/parser-7.7.4.tgz?cache=0&sync_timestamp=1574465979914&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fparser%2Fdownload%2F%40babel%2Fparser-7.7.4.tgz"
  "version" "7.7.4"

"@babel/plugin-proposal-async-generator-functions@^7.2.0":
  "integrity" "sha1-A1HFrAqeknhF//1bgq9HaUe3zm0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-remap-async-to-generator" "^7.7.4"
    "@babel/plugin-syntax-async-generators" "^7.7.4"

"@babel/plugin-proposal-class-properties@^7.0.0":
  "integrity" "sha1-L5ZPDLGLlIRQNidC4z4VIR53wro="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-proposal-decorators@^7.1.0":
  "integrity" "sha1-WMHiHSHqEvn18KdX5G5oe5Snqys="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.7.4.tgz?cache=0&sync_timestamp=1574466629563&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-decorators%2Fdownload%2F%40babel%2Fplugin-proposal-decorators-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-decorators" "^7.7.4"

"@babel/plugin-proposal-json-strings@^7.2.0":
  "integrity" "sha1-dwCmv9p3HY3IGXMknqxBbGtMaX0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-json-strings%2Fdownload%2F%40babel%2Fplugin-proposal-json-strings-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-json-strings" "^7.7.4"

"@babel/plugin-proposal-object-rest-spread@^7.3.4":
  "integrity" "sha1-zFeEmJSlx3QhQXjIq2T2M07Ir3E="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-proposal-object-rest-spread-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.7.4"

"@babel/plugin-proposal-optional-catch-binding@^7.2.0":
  "integrity" "sha1-7CHorrCexnEbwKOcpJUgq+4d43k="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-optional-catch-binding%2Fdownload%2F%40babel%2Fplugin-proposal-optional-catch-binding-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.7.4"

"@babel/plugin-proposal-unicode-property-regex@^7.2.0":
  "integrity" "sha1-fCOcyvCUcNvh1FPVAFdGDoRRfrs="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-unicode-property-regex%2Fdownload%2F%40babel%2Fplugin-proposal-unicode-property-regex-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-async-generators@^7.2.0", "@babel/plugin-syntax-async-generators@^7.7.4":
  "integrity" "sha1-MxqvMQoQyAxEpmsji25JEyvTyIk="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-async-generators%2Fdownload%2F%40babel%2Fplugin-syntax-async-generators-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-decorators@^7.7.4":
  "integrity" "sha1-PJHP7ioRFmP/OsIbhRFA9aUqTgs="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  "integrity" "sha1-Kco7RBWr/kpew4HpA4Yq0aVMOuw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-json-strings@^7.2.0", "@babel/plugin-syntax-json-strings@^7.7.4":
  "integrity" "sha1-huY/fS4i+eJxKaxOg+qYmjguhsw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-json-strings%2Fdownload%2F%40babel%2Fplugin-syntax-json-strings-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0":
  "integrity" "sha1-2rK1ajb7bDwiKh+8cfe/l/Mnqew="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.7.4.tgz?cache=0&sync_timestamp=1574466633839&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-jsx%2Fdownload%2F%40babel%2Fplugin-syntax-jsx-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.2.0", "@babel/plugin-syntax-object-rest-spread@^7.7.4":
  "integrity" "sha1-R88iDRnW0NexVDBHAfRo/BzG/0Y="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.7.4.tgz?cache=0&sync_timestamp=1574466110812&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-syntax-object-rest-spread-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-optional-catch-binding@^7.2.0", "@babel/plugin-syntax-optional-catch-binding@^7.7.4":
  "integrity" "sha1-o+OPWfS2IzhntKktyw7gWywzSqY="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-optional-catch-binding%2Fdownload%2F%40babel%2Fplugin-syntax-optional-catch-binding-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-arrow-functions@^7.2.0":
  "integrity" "sha1-djCb1Xit3YruOzedgJyAIwWpihI="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-arrow-functions%2Fdownload%2F%40babel%2Fplugin-transform-arrow-functions-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-async-to-generator@^7.3.4":
  "integrity" "sha1-aUy+rm1hOjTvApJxP6QvtFxEcLo="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-module-imports" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-remap-async-to-generator" "^7.7.4"

"@babel/plugin-transform-block-scoped-functions@^7.2.0":
  "integrity" "sha1-0NnVwmnHjq6nYies4hS40B5Ng3s="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoped-functions%2Fdownload%2F%40babel%2Fplugin-transform-block-scoped-functions-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-block-scoping@^7.3.4":
  "integrity" "sha1-IAqtDc1ruANy+U2eYo6gYsWL8iQ="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoping%2Fdownload%2F%40babel%2Fplugin-transform-block-scoping-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "lodash" "^4.17.13"

"@babel/plugin-transform-classes@^7.3.4":
  "integrity" "sha1-ySwUvgoTmeFd9yZnBnqPUQyUAOw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.7.4"
    "@babel/helper-define-map" "^7.7.4"
    "@babel/helper-function-name" "^7.7.4"
    "@babel/helper-optimise-call-expression" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.7.4"
    "@babel/helper-split-export-declaration" "^7.7.4"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.2.0":
  "integrity" "sha1-6FbBYo0yOP/hLWaOtCVZ95qBkQ0="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-computed-properties%2Fdownload%2F%40babel%2Fplugin-transform-computed-properties-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-destructuring@^7.2.0":
  "integrity" "sha1-K3E3KeUFShE1CXtqZ9obb+h4kmc="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-destructuring%2Fdownload%2F%40babel%2Fplugin-transform-destructuring-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-dotall-regex@^7.2.0":
  "integrity" "sha1-98zaYRGMW3olmactXjIQiEoCHpY="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-dotall-regex%2Fdownload%2F%40babel%2Fplugin-transform-dotall-regex-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-duplicate-keys@^7.2.0":
  "integrity" "sha1-PSFzGkLj9ZinODUpndAWnDuQrJE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-duplicate-keys%2Fdownload%2F%40babel%2Fplugin-transform-duplicate-keys-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-exponentiation-operator@^7.2.0":
  "integrity" "sha1-3TDAGR46G6GbzH44m9/dwHKdXbk="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-for-of@^7.2.0":
  "integrity" "sha1-JIgA46XlB7HxA9i0ypmOd8Y5Mrw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-for-of%2Fdownload%2F%40babel%2Fplugin-transform-for-of-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-function-name@^7.2.0":
  "integrity" "sha1-dabTMD1Q22OP+LU4XRJFHIZQJbE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-function-name" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-literals@^7.2.0":
  "integrity" "sha1-J/6H0rUBeipaNNHEGmufamJiZD4="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-literals%2Fdownload%2F%40babel%2Fplugin-transform-literals-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-modules-amd@^7.2.0":
  "integrity" "sha1-J2s4RcorIo8pleRTrcLm9U1y+3E="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-module-transforms" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "babel-plugin-dynamic-import-node" "^2.3.0"

"@babel/plugin-transform-modules-commonjs@^7.2.0":
  "integrity" "sha1-vuQ4blUERjQ91SpXHtpHhR/4V6M="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-module-transforms" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-simple-access" "^7.7.4"
    "babel-plugin-dynamic-import-node" "^2.3.0"

"@babel/plugin-transform-modules-systemjs@^7.3.4":
  "integrity" "sha1-zZgVIznT52Pf6Di31Cc+2vUguzA="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-hoist-variables" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "babel-plugin-dynamic-import-node" "^2.3.0"

"@babel/plugin-transform-modules-umd@^7.2.0":
  "integrity" "sha1-ECfDVaEY3gqun+4ArXgTxYTZBh8="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-module-transforms" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-named-capturing-groups-regex@^7.3.0":
  "integrity" "sha1-+zvMTuQZjnOFgFAHNz1rb0LJgiA="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-named-capturing-groups-regex%2Fdownload%2F%40babel%2Fplugin-transform-named-capturing-groups-regex-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.7.4"

"@babel/plugin-transform-new-target@^7.0.0":
  "integrity" "sha1-SgdT0tYGOUN74HtZKp5Y7gByAWc="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-new-target%2Fdownload%2F%40babel%2Fplugin-transform-new-target-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-object-super@^7.2.0":
  "integrity" "sha1-SEiJN6LVhsAUhFG/Ua+dfdpWcmI="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.7.4"

"@babel/plugin-transform-parameters@^7.2.0":
  "integrity" "sha1-2kVVyX85tRrAidMcc4DwO8pAdc4="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-call-delegate" "^7.7.4"
    "@babel/helper-get-function-arity" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-regenerator@^7.3.4":
  "integrity" "sha1-0Y6sAxKnAVLX2RTL7S3DmZYBz8A="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-regenerator%2Fdownload%2F%40babel%2Fplugin-transform-regenerator-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "regenerator-transform" "^0.14.0"

"@babel/plugin-transform-runtime@^7.4.0":
  "integrity" "sha1-Uf5FjBwfqYqLB5NPTtOLbNYhd6Y="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-module-imports" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "resolve" "^1.8.1"
    "semver" "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.2.0":
  "integrity" "sha1-dKCpsvbWemhMb7/V8EWOt7qZiR4="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-shorthand-properties%2Fdownload%2F%40babel%2Fplugin-transform-shorthand-properties-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-spread@^7.2.0":
  "integrity" "sha1-qmc7NW/mt+cNabbjOhf+9kEAhXg="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-spread%2Fdownload%2F%40babel%2Fplugin-transform-spread-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-sticky-regex@^7.2.0":
  "integrity" "sha1-/7aMBQkMMHMgdrEoXcFAG0BKEjw="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-sticky-regex%2Fdownload%2F%40babel%2Fplugin-transform-sticky-regex-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.0.0"

"@babel/plugin-transform-template-literals@^7.2.0":
  "integrity" "sha1-HrZBFzbdP+h9vSDMZmjlEhwX1gQ="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.7.4.tgz?cache=0&sync_timestamp=1574466265144&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-template-literals%2Fdownload%2F%40babel%2Fplugin-transform-template-literals-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-typeof-symbol@^7.2.0":
  "integrity" "sha1-MXRiYhTy1t4yKILkmKOOg3GyFA4="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-typeof-symbol%2Fdownload%2F%40babel%2Fplugin-transform-typeof-symbol-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-unicode-regex@^7.2.0":
  "integrity" "sha1-o8D2WxF8TIHFtkhPKl57lTRrg64="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-unicode-regex%2Fdownload%2F%40babel%2Fplugin-transform-unicode-regex-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.7.4"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/preset-env@^7.0.0 < 7.4.0":
  "integrity" "sha1-iHzzi20jyC8ZtRNSmL2xYAYuM+E="
  "resolved" "https://registry.npm.taobao.org/@babel/preset-env/download/@babel/preset-env-7.3.4.tgz"
  "version" "7.3.4"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.2.0"
    "@babel/plugin-proposal-json-strings" "^7.2.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.3.4"
    "@babel/plugin-proposal-optional-catch-binding" "^7.2.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.2.0"
    "@babel/plugin-syntax-async-generators" "^7.2.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"
    "@babel/plugin-transform-arrow-functions" "^7.2.0"
    "@babel/plugin-transform-async-to-generator" "^7.3.4"
    "@babel/plugin-transform-block-scoped-functions" "^7.2.0"
    "@babel/plugin-transform-block-scoping" "^7.3.4"
    "@babel/plugin-transform-classes" "^7.3.4"
    "@babel/plugin-transform-computed-properties" "^7.2.0"
    "@babel/plugin-transform-destructuring" "^7.2.0"
    "@babel/plugin-transform-dotall-regex" "^7.2.0"
    "@babel/plugin-transform-duplicate-keys" "^7.2.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.2.0"
    "@babel/plugin-transform-for-of" "^7.2.0"
    "@babel/plugin-transform-function-name" "^7.2.0"
    "@babel/plugin-transform-literals" "^7.2.0"
    "@babel/plugin-transform-modules-amd" "^7.2.0"
    "@babel/plugin-transform-modules-commonjs" "^7.2.0"
    "@babel/plugin-transform-modules-systemjs" "^7.3.4"
    "@babel/plugin-transform-modules-umd" "^7.2.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.3.0"
    "@babel/plugin-transform-new-target" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.2.0"
    "@babel/plugin-transform-parameters" "^7.2.0"
    "@babel/plugin-transform-regenerator" "^7.3.4"
    "@babel/plugin-transform-shorthand-properties" "^7.2.0"
    "@babel/plugin-transform-spread" "^7.2.0"
    "@babel/plugin-transform-sticky-regex" "^7.2.0"
    "@babel/plugin-transform-template-literals" "^7.2.0"
    "@babel/plugin-transform-typeof-symbol" "^7.2.0"
    "@babel/plugin-transform-unicode-regex" "^7.2.0"
    "browserslist" "^4.3.4"
    "invariant" "^2.2.2"
    "js-levenshtein" "^1.1.3"
    "semver" "^5.3.0"

"@babel/runtime-corejs2@^7.2.0":
  "integrity" "sha1-ucKxtYgnYgBXhbxHdAGVoKx4CIg="
  "resolved" "https://registry.npm.taobao.org/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "core-js" "^2.6.5"
    "regenerator-runtime" "^0.13.2"

"@babel/runtime@^7.0.0":
  "integrity" "sha1-sjqFZ1HkvwmSYvhndniJwOP+F1s="
  "resolved" "https://registry.npm.taobao.org/@babel/runtime/download/@babel/runtime-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "regenerator-runtime" "^0.13.2"

"@babel/template@^7.4.0", "@babel/template@^7.7.4":
  "integrity" "sha1-Qop9nuz/4n3qwKmOI7+ONnXSp3s="
  "resolved" "https://registry.npm.taobao.org/@babel/template/download/@babel/template-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.1.0", "@babel/traverse@^7.4.3", "@babel/traverse@^7.7.4":
  "integrity" "sha1-nB58YPtnn+T8+qQlAIMzM8IFhVg="
  "resolved" "https://registry.npm.taobao.org/@babel/traverse/download/@babel/traverse-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.7.4"
    "@babel/helper-function-name" "^7.7.4"
    "@babel/helper-split-export-declaration" "^7.7.4"
    "@babel/parser" "^7.7.4"
    "@babel/types" "^7.7.4"
    "debug" "^4.1.0"
    "globals" "^11.1.0"
    "lodash" "^4.17.13"

"@babel/types@^7.0.0", "@babel/types@^7.3.0", "@babel/types@^7.4.0", "@babel/types@^7.7.4":
  "integrity" "sha1-UWVw1TnkTd8wjAdWnCWP+U/ekZM="
  "resolved" "https://registry.npm.taobao.org/@babel/types/download/@babel/types-7.7.4.tgz?cache=0&sync_timestamp=1574465993660&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.7.4.tgz"
  "version" "7.7.4"
  dependencies:
    "esutils" "^2.0.2"
    "lodash" "^4.17.13"
    "to-fast-properties" "^2.0.0"

"@cnakazawa/watch@^1.0.3":
  "integrity" "sha1-CZE56ux+vweifBeGo/9k85Rk0u8="
  "resolved" "https://registry.npm.taobao.org/@cnakazawa/watch/download/@cnakazawa/watch-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "exec-sh" "^0.3.2"
    "minimist" "^1.2.0"

"@cypress/listr-verbose-renderer@0.4.1":
  "integrity" "sha1-p3SS9LEdzHxEajSz4ochr9M8ZCo="
  "resolved" "https://registry.npm.taobao.org/@cypress/listr-verbose-renderer/download/@cypress/listr-verbose-renderer-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "chalk" "^1.1.3"
    "cli-cursor" "^1.0.2"
    "date-fns" "^1.27.2"
    "figures" "^1.7.0"

"@cypress/xvfb@1.2.4":
  "integrity" "sha1-La9C6CdbOfSqU8FCFOVXvRTndIo="
  "resolved" "https://registry.npm.taobao.org/@cypress/xvfb/download/@cypress/xvfb-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "debug" "^3.1.0"
    "lodash.once" "^4.1.1"

"@hapi/address@2.x.x":
  "integrity" "sha1-XWftQ/P9QaadS5/3tW58DR0KgeU="
  "resolved" "https://registry.npm.taobao.org/@hapi/address/download/@hapi/address-2.1.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Faddress%2Fdownload%2F%40hapi%2Faddress-2.1.4.tgz"
  "version" "2.1.4"

"@hapi/bourne@1.x.x":
  "integrity" "sha1-CnCVreoGckPOMoPhtWuKj0U7JCo="
  "resolved" "https://registry.npm.taobao.org/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  "version" "1.3.2"

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  "integrity" "sha1-L5zjAciJjhwySLCoVkaWsk0amlo="
  "resolved" "https://registry.npm.taobao.org/@hapi/hoek/download/@hapi/hoek-8.5.0.tgz"
  "version" "8.5.0"

"@hapi/joi@^15.0.1":
  "integrity" "sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc="
  "resolved" "https://registry.npm.taobao.org/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  "version" "15.1.1"
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  "integrity" "sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck="
  "resolved" "https://registry.npm.taobao.org/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  "integrity" "sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg="
  "resolved" "https://registry.npm.taobao.org/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "cssnano" "^4.0.0"
    "cssnano-preset-default" "^4.0.0"
    "postcss" "^7.0.0"

"@jest/console@^24.7.1", "@jest/console@^24.9.0":
  "integrity" "sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A="
  "resolved" "https://registry.npm.taobao.org/@jest/console/download/@jest/console-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/source-map" "^24.9.0"
    "chalk" "^2.0.1"
    "slash" "^2.0.0"

"@jest/core@^24.9.0":
  "integrity" "sha1-LOzNC5MYH5xIUOdPKprUPTUTacQ="
  "resolved" "https://registry.npm.taobao.org/@jest/core/download/@jest/core-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.7.1"
    "@jest/reporters" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.1.15"
    "jest-changed-files" "^24.9.0"
    "jest-config" "^24.9.0"
    "jest-haste-map" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-regex-util" "^24.3.0"
    "jest-resolve" "^24.9.0"
    "jest-resolve-dependencies" "^24.9.0"
    "jest-runner" "^24.9.0"
    "jest-runtime" "^24.9.0"
    "jest-snapshot" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-validate" "^24.9.0"
    "jest-watcher" "^24.9.0"
    "micromatch" "^3.1.10"
    "p-each-series" "^1.0.0"
    "realpath-native" "^1.1.0"
    "rimraf" "^2.5.4"
    "slash" "^2.0.0"
    "strip-ansi" "^5.0.0"

"@jest/environment@^24.9.0":
  "integrity" "sha1-IeOvotZcBYbL1svv4gi6+t5Eqxg="
  "resolved" "https://registry.npm.taobao.org/@jest/environment/download/@jest/environment-24.9.0.tgz?cache=0&sync_timestamp=1566445333749&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fenvironment%2Fdownload%2F%40jest%2Fenvironment-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/fake-timers" "^24.9.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "jest-mock" "^24.9.0"

"@jest/fake-timers@^24.9.0":
  "integrity" "sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM="
  "resolved" "https://registry.npm.taobao.org/@jest/fake-timers/download/@jest/fake-timers-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-mock" "^24.9.0"

"@jest/reporters@^24.9.0":
  "integrity" "sha1-hmYO/44rlmHQQqjpigKLjWMaW0M="
  "resolved" "https://registry.npm.taobao.org/@jest/reporters/download/@jest/reporters-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/environment" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "chalk" "^2.0.1"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "istanbul-lib-coverage" "^2.0.2"
    "istanbul-lib-instrument" "^3.0.1"
    "istanbul-lib-report" "^2.0.4"
    "istanbul-lib-source-maps" "^3.0.1"
    "istanbul-reports" "^2.2.6"
    "jest-haste-map" "^24.9.0"
    "jest-resolve" "^24.9.0"
    "jest-runtime" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-worker" "^24.6.0"
    "node-notifier" "^5.4.2"
    "slash" "^2.0.0"
    "source-map" "^0.6.0"
    "string-length" "^2.0.0"

"@jest/source-map@^24.3.0", "@jest/source-map@^24.9.0":
  "integrity" "sha1-DiY6lEML5LQdpoPMwea//ioZFxQ="
  "resolved" "https://registry.npm.taobao.org/@jest/source-map/download/@jest/source-map-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.1.15"
    "source-map" "^0.6.0"

"@jest/test-result@^24.9.0":
  "integrity" "sha1-EXluiqnb+I6gJXV7MVJZWtBroMo="
  "resolved" "https://registry.npm.taobao.org/@jest/test-result/download/@jest/test-result-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/istanbul-lib-coverage" "^2.0.0"

"@jest/test-sequencer@^24.9.0":
  "integrity" "sha1-+PM081tiWk8vNV8v5+YDba0uazE="
  "resolved" "https://registry.npm.taobao.org/@jest/test-sequencer/download/@jest/test-sequencer-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/test-result" "^24.9.0"
    "jest-haste-map" "^24.9.0"
    "jest-runner" "^24.9.0"
    "jest-runtime" "^24.9.0"

"@jest/transform@^24.9.0":
  "integrity" "sha1-SuJ2iyllU/rasJ6ewRlUPJCxbFY="
  "resolved" "https://registry.npm.taobao.org/@jest/transform/download/@jest/transform-24.9.0.tgz?cache=0&sync_timestamp=1566445333974&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ftransform%2Fdownload%2F%40jest%2Ftransform-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^24.9.0"
    "babel-plugin-istanbul" "^5.1.0"
    "chalk" "^2.0.1"
    "convert-source-map" "^1.4.0"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.1.15"
    "jest-haste-map" "^24.9.0"
    "jest-regex-util" "^24.9.0"
    "jest-util" "^24.9.0"
    "micromatch" "^3.1.10"
    "pirates" "^4.0.1"
    "realpath-native" "^1.1.0"
    "slash" "^2.0.0"
    "source-map" "^0.6.1"
    "write-file-atomic" "2.4.1"

"@jest/types@^24.9.0":
  "integrity" "sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk="
  "resolved" "https://registry.npm.taobao.org/@jest/types/download/@jest/types-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^13.0.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  "integrity" "sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4="
  "resolved" "https://registry.npm.taobao.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "call-me-maybe" "^1.0.1"
    "glob-to-regexp" "^0.3.0"

"@nodelib/fs.stat@^1.1.2":
  "integrity" "sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs="
  "resolved" "https://registry.npm.taobao.org/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"
  "version" "1.1.3"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  "integrity" "sha1-cG9kvLSouWQrSK46zkRMcDNNYV0="
  "resolved" "https://registry.npm.taobao.org/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "chalk" "^1.1.3"
    "error-stack-parser" "^2.0.0"
    "string-width" "^2.0.0"

"@types/babel__core@^7.1.0":
  "integrity" "sha1-5EHqffY80IDfzQKrGZ5tFqc1/DA="
  "resolved" "https://registry.npm.taobao.org/@types/babel__core/download/@types/babel__core-7.1.3.tgz"
  "version" "7.1.3"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha1-8ewcEE0btGNVbstyQBireI0MFyo="
  "resolved" "https://registry.npm.taobao.org/@types/babel__generator/download/@types/babel__generator-7.6.0.tgz"
  "version" "7.6.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha1-T/Y9a1Lt2sHee5daUiPtMuzqkwc="
  "resolved" "https://registry.npm.taobao.org/@types/babel__template/download/@types/babel__template-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  "integrity" "sha1-R5pO4+KRpAOhCWEGAT7CLPm2QBI="
  "resolved" "https://registry.npm.taobao.org/@types/babel__traverse/download/@types/babel__traverse-7.0.8.tgz"
  "version" "7.0.8"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/color-name@^1.1.1":
  "integrity" "sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA="
  "resolved" "https://registry.npm.taobao.org/@types/color-name/download/@types/color-name-1.1.1.tgz"
  "version" "1.1.1"

"@types/echarts@^4.4.6":
  "integrity" "sha512-xqZ21MXOBU5i+BywnxAi0He+M6lsRtANBvbqJHmqM8T64Lx7azHJWbv/vSKVJfEbv3XyYpxeDhpV7gFi5mfgeA=="
  "resolved" "https://registry.npmjs.org/@types/echarts/-/echarts-4.9.13.tgz"
  "version" "4.9.13"
  dependencies:
    "@types/zrender" "*"

"@types/eslint-visitor-keys@^1.0.0":
  "integrity" "sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0="
  "resolved" "https://registry.npm.taobao.org/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz"
  "version" "1.0.0"

"@types/events@*":
  "integrity" "sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc="
  "resolved" "https://registry.npm.taobao.org/@types/events/download/@types/events-3.0.0.tgz"
  "version" "3.0.0"

"@types/glob@^7.1.1":
  "integrity" "sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU="
  "resolved" "https://registry.npm.taobao.org/@types/glob/download/@types/glob-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "@types/events" "*"
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  "integrity" "sha1-QplbRG25pIoRoH7Ag0mahg6ROP8="
  "resolved" "https://registry.npm.taobao.org/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.1.tgz"
  "version" "2.0.1"

"@types/istanbul-lib-report@*":
  "integrity" "sha1-5Ucef6M8YTWN04QmGJwDelhDO4w="
  "resolved" "https://registry.npm.taobao.org/@types/istanbul-lib-report/download/@types/istanbul-lib-report-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  "integrity" "sha1-eoy/akBvNsit2HFiWyeOrwsNJVo="
  "resolved" "https://registry.npm.taobao.org/@types/istanbul-reports/download/@types/istanbul-reports-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/jest@^24.0.18":
  "integrity" "sha1-BG+OKt4Cb+gxYj42Gja2+5pEY+Q="
  "resolved" "https://registry.npm.taobao.org/@types/jest/download/@types/jest-24.0.23.tgz?cache=0&sync_timestamp=1573592350097&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fjest%2Fdownload%2F%40types%2Fjest-24.0.23.tgz"
  "version" "24.0.23"
  dependencies:
    "jest-diff" "^24.3.0"

"@types/js-cookie@^2.2.2":
  "integrity" "sha1-95cgtHVaoZfC4V6YLi9Dj1dI40g="
  "resolved" "https://registry.npm.taobao.org/@types/js-cookie/download/@types/js-cookie-2.2.4.tgz"
  "version" "2.2.4"

"@types/json-schema@^7.0.3":
  "integrity" "sha1-vf1p1h5GTcyBslFZwnDXWnPBpjY="
  "resolved" "https://registry.npm.taobao.org/@types/json-schema/download/@types/json-schema-7.0.3.tgz?cache=0&sync_timestamp=1572463317397&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fjson-schema%2Fdownload%2F%40types%2Fjson-schema-7.0.3.tgz"
  "version" "7.0.3"

"@types/minimatch@*":
  "integrity" "sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0="
  "resolved" "https://registry.npm.taobao.org/@types/minimatch/download/@types/minimatch-3.0.3.tgz"
  "version" "3.0.3"

"@types/node@*":
  "integrity" "sha1-HB1uPHXbpGbgMmlI1W6L1yoZA9I="
  "resolved" "https://registry.npm.taobao.org/@types/node/download/@types/node-12.12.14.tgz"
  "version" "12.12.14"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4="
  "resolved" "https://registry.npm.taobao.org/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz"
  "version" "2.4.0"

"@types/nprogress@^0.2.0":
  "integrity" "sha1-hsWTaC1BmSEqBQnMPE1WK7vW5F8="
  "resolved" "https://registry.npm.taobao.org/@types/nprogress/download/@types/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"@types/q@^1.5.1":
  "integrity" "sha1-aQoUdbhPKohP0HzXl8APXzE1bqg="
  "resolved" "https://registry.npm.taobao.org/@types/q/download/@types/q-1.5.2.tgz?cache=0&sync_timestamp=1572589392471&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fq%2Fdownload%2F%40types%2Fq-1.5.2.tgz"
  "version" "1.5.2"

"@types/sizzle@2.3.2":
  "integrity" "sha1-qBG4wY4rq6t9VCszZYh64uTZ3kc="
  "resolved" "https://registry.npm.taobao.org/@types/sizzle/download/@types/sizzle-2.3.2.tgz"
  "version" "2.3.2"

"@types/source-list-map@*":
  "integrity" "sha512-K5K+yml8LTo9bWJI/rECfIPrGgxdpeNbj+d53lwN4QjW1MCwlkhUms+gtdzigTeUyBr09+u8BwOIY3MXvHdcsA=="
  "resolved" "https://registry.npmjs.org/@types/source-list-map/-/source-list-map-0.1.2.tgz"
  "version" "0.1.2"

"@types/stack-utils@^1.0.1":
  "integrity" "sha1-CoUdO9lkmPolwzq3J47TvWXwbD4="
  "resolved" "https://registry.npm.taobao.org/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz"
  "version" "1.0.1"

"@types/strip-bom@^3.0.0":
  "integrity" "sha1-FKjsOVbC6B7bdSB5CuzyHCkK69I="
  "resolved" "https://registry.npm.taobao.org/@types/strip-bom/download/@types/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"@types/strip-json-comments@0.0.30":
  "integrity" "sha1-mqMMBNshKpoGSdaub9UKzMQHSKE="
  "resolved" "https://registry.npm.taobao.org/@types/strip-json-comments/download/@types/strip-json-comments-0.0.30.tgz"
  "version" "0.0.30"

"@types/tapable@^1":
  "integrity" "sha512-ipixuVrh2OdNmauvtT51o3d8z12p6LtFW9in7U79der/kwejjdNchQC5UMn5u/KxNoM7VHHOs/l8KS8uHxhODQ=="
  "resolved" "https://registry.npmjs.org/@types/tapable/-/tapable-1.0.8.tgz"
  "version" "1.0.8"

"@types/uglify-js@*":
  "integrity" "sha512-/xFrPIo+4zOeNGtVMbf9rUm0N+i4pDf1ynExomqtokIJmVzR3962lJ1UE+MmexMkA0cmN9oTzg5Xcbwge0Ij2Q=="
  "resolved" "https://registry.npmjs.org/@types/uglify-js/-/uglify-js-3.13.2.tgz"
  "version" "3.13.2"
  dependencies:
    "source-map" "^0.6.1"

"@types/webpack-env@^1.13.9", "@types/webpack-env@^1.14.0":
  "integrity" "sha1-DYpT8wjwF8U6Xdw9B/TW+na3kNc="
  "resolved" "https://registry.npm.taobao.org/@types/webpack-env/download/@types/webpack-env-1.14.1.tgz"
  "version" "1.14.1"

"@types/webpack-sources@*":
  "integrity" "sha512-Ft7YH3lEVRQ6ls8k4Ff1oB4jN6oy/XmU6tQISKdhfh+1mR+viZFphS6WL0IrtDOzvefmJg5a0s7ZQoRXwqTEFg=="
  "resolved" "https://registry.npmjs.org/@types/webpack-sources/-/webpack-sources-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    "source-map" "^0.7.3"

"@types/webpack@^4.41.12":
  "integrity" "sha512-cb+0ioil/7oz5//7tZUSwbrSAN/NWHrQylz5cW8G0dWTcF/g+/dSdMlKVZspBYuMAN1+WnwHrkxiRrLcwd0Heg=="
  "resolved" "https://registry.npmjs.org/@types/webpack/-/webpack-4.41.32.tgz"
  "version" "4.41.32"
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    "anymatch" "^3.0.0"
    "source-map" "^0.6.0"

"@types/yargs-parser@*":
  "integrity" "sha1-xWOqGS85NQodGNo2xajaOCu9gig="
  "resolved" "https://registry.npm.taobao.org/@types/yargs-parser/download/@types/yargs-parser-13.1.0.tgz"
  "version" "13.1.0"

"@types/yargs@^13.0.0":
  "integrity" "sha1-dkgq85gdRBLWU3GjGPmS0zRko4A="
  "resolved" "https://registry.npm.taobao.org/@types/yargs/download/@types/yargs-13.0.3.tgz"
  "version" "13.0.3"
  dependencies:
    "@types/yargs-parser" "*"

"@types/zrender@*":
  "integrity" "sha512-IyTRf30jPOXK1+1RChI/78U6aV9hyWYf/vhL96Vt66oDz9es/BDjeKpvbNZSOHVA7zAReOwJcmdZS5AGAqhygw=="
  "resolved" "https://registry.npmjs.org/@types/zrender/-/zrender-4.0.1.tgz"
  "version" "4.0.1"

"@typescript-eslint/eslint-plugin@^1.1.0":
  "integrity" "sha1-Iv7ZsW3f60Av17zeVjB4IPbrxJ8="
  "resolved" "https://registry.npm.taobao.org/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-1.13.0.tgz?cache=0&sync_timestamp=1575491460929&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Feslint-plugin%2Fdownload%2F%40typescript-eslint%2Feslint-plugin-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "@typescript-eslint/experimental-utils" "1.13.0"
    "eslint-utils" "^1.3.1"
    "functional-red-black-tree" "^1.0.1"
    "regexpp" "^2.0.1"
    "tsutils" "^3.7.0"

"@typescript-eslint/experimental-utils@1.13.0":
  "integrity" "sha1-sIxg14DABn3i+0SwS0MvVAE4MB4="
  "resolved" "https://registry.npm.taobao.org/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-1.13.0.tgz?cache=0&sync_timestamp=1575491458727&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Fexperimental-utils%2Fdownload%2F%40typescript-eslint%2Fexperimental-utils-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/typescript-estree" "1.13.0"
    "eslint-scope" "^4.0.0"

"@typescript-eslint/parser@^1.1.0", "@typescript-eslint/parser@^1.9.0":
  "integrity" "sha1-Yax4EepSeRxH3J/U3UoYT66aw1U="
  "resolved" "https://registry.npm.taobao.org/@typescript-eslint/parser/download/@typescript-eslint/parser-1.13.0.tgz?cache=0&sync_timestamp=1575491460372&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Fparser%2Fdownload%2F%40typescript-eslint%2Fparser-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "1.13.0"
    "@typescript-eslint/typescript-estree" "1.13.0"
    "eslint-visitor-keys" "^1.0.0"

"@typescript-eslint/typescript-estree@1.13.0":
  "integrity" "sha1-gUDxfQ9gwDYZeY8dYouENJE9wy4="
  "resolved" "https://registry.npm.taobao.org/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-1.13.0.tgz?cache=0&sync_timestamp=1575491459309&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Ftypescript-estree%2Fdownload%2F%40typescript-eslint%2Ftypescript-estree-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "lodash.unescape" "4.0.1"
    "semver" "5.5.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0":
  "integrity" "sha1-BI/leZWNpAj7eosqPsBQtQpmEEA="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.0.0.tgz"
  "version" "1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.1.2":
  "integrity" "sha1-wKPm78Ai515CR7RIqPxrhvA+kcA="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^3.12.1":
  "integrity" "sha1-JMR3BS8HjzD9t3NRA7FN0fosv+E="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-preset-app/download/@vue/babel-preset-app-3.12.1.tgz?cache=0&sync_timestamp=1574867940700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-preset-app%2Fdownload%2F%40vue%2Fbabel-preset-app-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-decorators" "^7.1.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.4.0"
    "@babel/preset-env" "^7.0.0 < 7.4.0"
    "@babel/runtime" "^7.0.0"
    "@babel/runtime-corejs2" "^7.2.0"
    "@vue/babel-preset-jsx" "^1.0.0"
    "babel-plugin-dynamic-import-node" "^2.2.0"
    "babel-plugin-module-resolver" "3.2.0"
    "core-js" "^2.6.5"

"@vue/babel-preset-jsx@^1.0.0":
  "integrity" "sha1-LhaetMIE6jfKZsLqhaiAv8mdTyA="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "@vue/babel-sugar-functional-vue" "^1.1.2"
    "@vue/babel-sugar-inject-h" "^1.1.2"
    "@vue/babel-sugar-v-model" "^1.1.2"
    "@vue/babel-sugar-v-on" "^1.1.2"

"@vue/babel-sugar-functional-vue@^1.1.2":
  "integrity" "sha1-9+JPugnm8e5wEEVgqICAV1VfGpo="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.1.2":
  "integrity" "sha1-ilJ2ttji7Rb/yAeKrZQjYnTm7fA="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.1.2":
  "integrity" "sha1-H/b9G4ACI/ycsehNzrXlLXN6gZI="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.1.2":
  "integrity" "sha1-su+ZuPL6sJ++rSWq1w70Lhz1sTs="
  "resolved" "https://registry.npm.taobao.org/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^3.12.1":
  "integrity" "sha1-vf3o9xI1YasG5OTGC4VMxQkvWrE="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-overlay/download/@vue/cli-overlay-3.12.1.tgz?cache=0&sync_timestamp=1574867940002&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-overlay%2Fdownload%2F%40vue%2Fcli-overlay-3.12.1.tgz"
  "version" "3.12.1"

"@vue/cli-plugin-babel@^3.11.0":
  "integrity" "sha1-mnkVnejNCGsBP6bXijmDCy4uxwY="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-3.12.1.tgz?cache=0&sync_timestamp=1574867941549&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-plugin-babel%2Fdownload%2F%40vue%2Fcli-plugin-babel-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@babel/core" "^7.0.0"
    "@vue/babel-preset-app" "^3.12.1"
    "@vue/cli-shared-utils" "^3.12.1"
    "babel-loader" "^8.0.5"
    "webpack" "^4.0.0"

"@vue/cli-plugin-e2e-cypress@^3.11.0":
  "integrity" "sha1-fMUqEfFZ/bz2HsrCRO3lS/1mOsk="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-plugin-e2e-cypress/download/@vue/cli-plugin-e2e-cypress-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@vue/cli-shared-utils" "^3.12.1"
    "cypress" "^3.2.0"
    "eslint-plugin-cypress" "^2.2.1"

"@vue/cli-plugin-eslint@^3.11.0":
  "integrity" "sha1-MCxGOGfzjnkLuZbq/fcVnHgtyM8="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-3.12.1.tgz?cache=0&sync_timestamp=1574867942390&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-plugin-eslint%2Fdownload%2F%40vue%2Fcli-plugin-eslint-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@vue/cli-shared-utils" "^3.12.1"
    "babel-eslint" "^10.0.1"
    "eslint-loader" "^2.1.2"
    "globby" "^9.2.0"
    "webpack" "^4.0.0"
    "yorkie" "^2.0.0"
  optionalDependencies:
    "eslint" "^4.19.1"
    "eslint-plugin-vue" "^4.7.1"

"@vue/cli-plugin-pwa@^3.11.0":
  "integrity" "sha1-A8mXi2QKPLzgK3kiEDoTGogGPzE="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-plugin-pwa/download/@vue/cli-plugin-pwa-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@vue/cli-shared-utils" "^3.12.1"
    "webpack" "^4.0.0"
    "workbox-webpack-plugin" "^3.6.3"

"@vue/cli-plugin-typescript@^3.11.0":
  "integrity" "sha1-cbMGmD3jegPEOGCsA1vQoV6ynSc="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-plugin-typescript/download/@vue/cli-plugin-typescript-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@types/webpack-env" "^1.13.9"
    "@vue/cli-shared-utils" "^3.12.1"
    "fork-ts-checker-webpack-plugin" "^0.5.2"
    "globby" "^9.2.0"
    "ts-loader" "^5.3.3"
    "tslint" "^5.15.0"
    "webpack" "^4.0.0"
    "yorkie" "^2.0.0"

"@vue/cli-plugin-unit-jest@^3.11.0":
  "integrity" "sha1-t+3TBwEZHeqp1TzqdSuNcrgl1kA="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-plugin-unit-jest/download/@vue/cli-plugin-unit-jest-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@vue/cli-shared-utils" "^3.12.1"
    "babel-jest" "^23.6.0"
    "babel-plugin-transform-es2015-modules-commonjs" "^6.26.2"
    "jest" "^23.6.0"
    "jest-serializer-vue" "^2.0.2"
    "jest-transform-stub" "^2.0.0"
    "jest-watch-typeahead" "0.2.1"
    "vue-jest" "^3.0.4"

"@vue/cli-service@^3.11.0":
  "integrity" "sha1-EyILHBiSVOfAAzkN8ykIb5tud+Y="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-service/download/@vue/cli-service-3.12.1.tgz?cache=0&sync_timestamp=1574867942808&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-service%2Fdownload%2F%40vue%2Fcli-service-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@vue/cli-overlay" "^3.12.1"
    "@vue/cli-shared-utils" "^3.12.1"
    "@vue/component-compiler-utils" "^3.0.0"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    "acorn" "^6.1.1"
    "acorn-walk" "^6.1.1"
    "address" "^1.0.3"
    "autoprefixer" "^9.5.1"
    "browserslist" "^4.5.4"
    "cache-loader" "^2.0.1"
    "case-sensitive-paths-webpack-plugin" "^2.2.0"
    "chalk" "^2.4.2"
    "cli-highlight" "^2.1.0"
    "clipboardy" "^2.0.0"
    "cliui" "^5.0.0"
    "copy-webpack-plugin" "^4.6.0"
    "css-loader" "^1.0.1"
    "cssnano" "^4.1.10"
    "current-script-polyfill" "^1.0.0"
    "debug" "^4.1.1"
    "default-gateway" "^5.0.2"
    "dotenv" "^7.0.0"
    "dotenv-expand" "^5.1.0"
    "escape-string-regexp" "^1.0.5"
    "file-loader" "^3.0.1"
    "fs-extra" "^7.0.1"
    "globby" "^9.2.0"
    "hash-sum" "^1.0.2"
    "html-webpack-plugin" "^3.2.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "lodash.transform" "^4.6.0"
    "mini-css-extract-plugin" "^0.8.0"
    "minimist" "^1.2.0"
    "ora" "^3.4.0"
    "portfinder" "^1.0.20"
    "postcss-loader" "^3.0.0"
    "read-pkg" "^5.0.0"
    "semver" "^6.0.0"
    "slash" "^2.0.0"
    "source-map-url" "^0.4.0"
    "ssri" "^6.0.1"
    "string.prototype.padend" "^3.0.0"
    "terser-webpack-plugin" "^1.2.3"
    "thread-loader" "^2.1.2"
    "url-loader" "^1.1.2"
    "vue-loader" "^15.7.0"
    "webpack" "^4.0.0"
    "webpack-bundle-analyzer" "^3.3.0"
    "webpack-chain" "^4.11.0"
    "webpack-dev-server" "^3.4.1"
    "webpack-merge" "^4.2.1"

"@vue/cli-shared-utils@^3.12.1":
  "integrity" "sha1-vPB2KH3a3uu7l8anSN/p/1DsjfA="
  "resolved" "https://registry.npm.taobao.org/@vue/cli-shared-utils/download/@vue/cli-shared-utils-3.12.1.tgz?cache=0&sync_timestamp=1574867941072&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-shared-utils%2Fdownload%2F%40vue%2Fcli-shared-utils-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@hapi/joi" "^15.0.1"
    "chalk" "^2.4.1"
    "execa" "^1.0.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^5.1.1"
    "node-ipc" "^9.1.1"
    "open" "^6.3.0"
    "ora" "^3.4.0"
    "request" "^2.87.0"
    "request-promise-native" "^1.0.7"
    "semver" "^6.0.0"
    "string.prototype.padstart" "^3.0.0"

"@vue/component-compiler-utils@^3.0.0":
  "integrity" "sha1-fa+Krw1fqmbnyKH2/qMVYw5F+8k="
  "resolved" "https://registry.npm.taobao.org/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.0.2.tgz?cache=0&sync_timestamp=1573021193473&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcomponent-compiler-utils%2Fdownload%2F%40vue%2Fcomponent-compiler-utils-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.14"
    "postcss-selector-parser" "^5.0.0"
    "prettier" "^1.18.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"

"@vue/eslint-config-standard@^4.0.0":
  "integrity" "sha1-a+RH7mdOOw9zPFhAmP2aIubXb80="
  "resolved" "https://registry.npm.taobao.org/@vue/eslint-config-standard/download/@vue/eslint-config-standard-4.0.0.tgz?cache=0&sync_timestamp=1575533899528&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Feslint-config-standard%2Fdownload%2F%40vue%2Feslint-config-standard-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "eslint-config-standard" "^12.0.0"
    "eslint-plugin-import" "^2.14.0"
    "eslint-plugin-node" "^8.0.0"
    "eslint-plugin-promise" "^4.0.1"
    "eslint-plugin-standard" "^4.0.0"

"@vue/eslint-config-typescript@^4.0.0":
  "integrity" "sha1-ogKYNZikqCZGDLuO5Dgmh1sPZnM="
  "resolved" "https://registry.npm.taobao.org/@vue/eslint-config-typescript/download/@vue/eslint-config-typescript-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@typescript-eslint/eslint-plugin" "^1.1.0"
    "@typescript-eslint/parser" "^1.1.0"

"@vue/preload-webpack-plugin@^1.1.0":
  "integrity" "sha1-GHI1MNME9EMCHaIpLW7JUCgmEEo="
  "resolved" "https://registry.npm.taobao.org/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fpreload-webpack-plugin%2Fdownload%2F%40vue%2Fpreload-webpack-plugin-1.1.1.tgz"
  "version" "1.1.1"

"@vue/test-utils@^1.0.0-beta.29":
  "integrity" "sha1-1fJtHiQR/bf6f97bYbS06kGUxJ0="
  "resolved" "https://registry.npm.taobao.org/@vue/test-utils/download/@vue/test-utils-1.0.0-beta.30.tgz"
  "version" "1.0.0-beta.30"
  dependencies:
    "dom-event-types" "^1.0.0"
    "lodash" "^4.17.15"
    "pretty" "^2.0.0"

"@vue/web-component-wrapper@^1.2.0":
  "integrity" "sha1-uw5G8VhafiibTuYGfcxaauYvHdE="
  "resolved" "https://registry.npm.taobao.org/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz"
  "version" "1.2.0"

"@webassemblyjs/ast@1.8.5":
  "integrity" "sha1-UbHF/mV2o0lTv0slPfnw1JDZ41k="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/ast/download/@webassemblyjs/ast-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/wast-parser" "1.8.5"

"@webassemblyjs/floating-point-hex-parser@1.8.5":
  "integrity" "sha1-G6kmopI2E+3OSW/VsC6M6KX0lyE="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.8.5.tgz"
  "version" "1.8.5"

"@webassemblyjs/helper-api-error@1.8.5":
  "integrity" "sha1-xJ2tIvZFInxe22EL25aX8aq3Ifc="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.8.5.tgz"
  "version" "1.8.5"

"@webassemblyjs/helper-buffer@1.8.5":
  "integrity" "sha1-/qk+Qphj3V5DOFVfQikjhaZT8gQ="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.8.5.tgz"
  "version" "1.8.5"

"@webassemblyjs/helper-code-frame@1.8.5":
  "integrity" "sha1-mnQP9I4/qjAisd/1RCPfmqKTwl4="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/wast-printer" "1.8.5"

"@webassemblyjs/helper-fsm@1.8.5":
  "integrity" "sha1-ugt9Oz9+RzPaYFnJMyJ12GBwJFI="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.8.5.tgz"
  "version" "1.8.5"

"@webassemblyjs/helper-module-context@1.8.5":
  "integrity" "sha1-3vS5knsBAdyMu9jR7bW3ucguskU="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "mamacro" "^0.0.3"

"@webassemblyjs/helper-wasm-bytecode@1.8.5":
  "integrity" "sha1-U3p1Dt31weky83RCBlUckcG5PmE="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.8.5.tgz"
  "version" "1.8.5"

"@webassemblyjs/helper-wasm-section@1.8.5":
  "integrity" "sha1-dMpqa8vhnlCjtrRihH5pUD5r/L8="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-buffer" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/wasm-gen" "1.8.5"

"@webassemblyjs/ieee754@1.8.5":
  "integrity" "sha1-cSMp2+8kDza/V70ve4+5v0FUQh4="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.8.5":
  "integrity" "sha1-BE7es06mefPgTNT9mCTV41dnrhA="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.8.5":
  "integrity" "sha1-qL87XY/+mGx8Hjc8y9wqCRXwztw="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.8.5.tgz"
  "version" "1.8.5"

"@webassemblyjs/wasm-edit@1.8.5":
  "integrity" "sha1-li2hKqWswcExyBxCMpkcgs5W4Bo="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-buffer" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/helper-wasm-section" "1.8.5"
    "@webassemblyjs/wasm-gen" "1.8.5"
    "@webassemblyjs/wasm-opt" "1.8.5"
    "@webassemblyjs/wasm-parser" "1.8.5"
    "@webassemblyjs/wast-printer" "1.8.5"

"@webassemblyjs/wasm-gen@1.8.5":
  "integrity" "sha1-VIQHZsLBAC62TtGr5yCt7XFPmLw="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/ieee754" "1.8.5"
    "@webassemblyjs/leb128" "1.8.5"
    "@webassemblyjs/utf8" "1.8.5"

"@webassemblyjs/wasm-opt@1.8.5":
  "integrity" "sha1-sk2fa6UDlK8TSfUQr6j/y4pj0mQ="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-buffer" "1.8.5"
    "@webassemblyjs/wasm-gen" "1.8.5"
    "@webassemblyjs/wasm-parser" "1.8.5"

"@webassemblyjs/wasm-parser@1.8.5":
  "integrity" "sha1-IVdvDsiLkUJzV7hTY4NmjvfGa40="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-api-error" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/ieee754" "1.8.5"
    "@webassemblyjs/leb128" "1.8.5"
    "@webassemblyjs/utf8" "1.8.5"

"@webassemblyjs/wast-parser@1.8.5":
  "integrity" "sha1-4Q7s1ULQ5705T2gnxJ899tTu+4w="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/floating-point-hex-parser" "1.8.5"
    "@webassemblyjs/helper-api-error" "1.8.5"
    "@webassemblyjs/helper-code-frame" "1.8.5"
    "@webassemblyjs/helper-fsm" "1.8.5"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.8.5":
  "integrity" "sha1-EUu8SB/RDKDiOzVg+oEnSLC65bw="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/wast-parser" "1.8.5"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="
  "resolved" "https://registry.npm.taobao.org/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="
  "resolved" "https://registry.npm.taobao.org/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  "version" "4.2.2"

"abab@^2.0.0":
  "integrity" "sha1-Yj4gdeAustPyR15J+ZyRhGRnkHo="
  "resolved" "https://registry.npm.taobao.org/abab/download/abab-2.0.3.tgz?cache=0&sync_timestamp=1573609024450&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fabab%2Fdownload%2Fabab-2.0.3.tgz"
  "version" "2.0.3"

"abbrev@1":
  "integrity" "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg="
  "resolved" "https://registry.npm.taobao.org/abbrev/download/abbrev-1.1.1.tgz"
  "version" "1.1.1"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha1-UxvHJlF6OytB+FACHGzBXqq1B80="
  "resolved" "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-globals@^4.1.0":
  "integrity" "sha1-n6GSat3BHJcwjE5m163Q1Awycuc="
  "resolved" "https://registry.npm.taobao.org/acorn-globals/download/acorn-globals-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "acorn" "^6.0.1"
    "acorn-walk" "^6.0.1"

"acorn-jsx@^3.0.0":
  "integrity" "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s="
  "resolved" "https://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "acorn" "^3.0.4"

"acorn-jsx@^5.0.0":
  "integrity" "sha1-KUrbcbVzmLBoABXwo4xWPuHbU4Q="
  "resolved" "https://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-5.1.0.tgz"
  "version" "5.1.0"

"acorn-jsx@^5.1.0":
  "integrity" "sha1-KUrbcbVzmLBoABXwo4xWPuHbU4Q="
  "resolved" "https://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-5.1.0.tgz"
  "version" "5.1.0"

"acorn-walk@^6.0.1", "acorn-walk@^6.1.1":
  "integrity" "sha1-Ejy487hMIXHx9/slJhWxx4prGow="
  "resolved" "https://registry.npm.taobao.org/acorn-walk/download/acorn-walk-6.2.0.tgz?cache=0&sync_timestamp=1565683180334&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-walk%2Fdownload%2Facorn-walk-6.2.0.tgz"
  "version" "6.2.0"

"acorn@^3.0.4":
  "integrity" "sha1-ReN/s56No/JbruP/U2niu18iAXo="
  "resolved" "https://registry.npm.taobao.org/acorn/download/acorn-3.3.0.tgz"
  "version" "3.3.0"

"acorn@^5.5.0":
  "integrity" "sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk="
  "resolved" "https://registry.npm.taobao.org/acorn/download/acorn-5.7.3.tgz"
  "version" "5.7.3"

"acorn@^5.5.3":
  "integrity" "sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk="
  "resolved" "https://registry.npm.taobao.org/acorn/download/acorn-5.7.3.tgz"
  "version" "5.7.3"

"acorn@^6.0.0 || ^7.0.0", "acorn@^6.0.1", "acorn@^6.0.2", "acorn@^6.0.7", "acorn@^6.1.1", "acorn@^6.2.1":
  "integrity" "sha1-tlnS/7r6JLr12xzbsslKmD7NJ4Q="
  "resolved" "https://registry.npm.taobao.org/acorn/download/acorn-6.4.0.tgz"
  "version" "6.4.0"

"acorn@^7.1.0":
  "integrity" "sha1-lJ028sKSU12mAig1hsJHfFfrLWw="
  "resolved" "https://registry.npm.taobao.org/acorn/download/acorn-7.1.0.tgz"
  "version" "7.1.0"

"address@^1.0.3":
  "integrity" "sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY="
  "resolved" "https://registry.npm.taobao.org/address/download/address-1.1.2.tgz"
  "version" "1.1.2"

"ajv-errors@^1.0.0":
  "integrity" "sha1-81mGrOuRr63sQQL72FAUlQzvpk0="
  "resolved" "https://registry.npm.taobao.org/ajv-errors/download/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^2.1.0":
  "integrity" "sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I="
  "resolved" "https://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-2.1.1.tgz"
  "version" "2.1.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.4.1":
  "integrity" "sha1-75FuJxxkrBIXH9g4TqrmsjRYVNo="
  "resolved" "https://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.4.1.tgz"
  "version" "3.4.1"

"ajv@^5.0.0", "ajv@^5.2.3":
  "integrity" "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU="
  "resolved" "https://registry.npm.taobao.org/ajv/download/ajv-5.5.2.tgz"
  "version" "5.5.2"
  dependencies:
    "co" "^4.6.0"
    "fast-deep-equal" "^1.0.0"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.3.0"

"ajv@^5.3.0":
  "integrity" "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU="
  "resolved" "https://registry.npm.taobao.org/ajv/download/ajv-5.5.2.tgz"
  "version" "5.5.2"
  dependencies:
    "co" "^4.6.0"
    "fast-deep-equal" "^1.0.0"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.3.0"

"ajv@^6.1.0", "ajv@^6.10.0", "ajv@^6.10.2", "ajv@^6.5.5", "ajv@^6.9.1", "ajv@>=5.0.0":
  "integrity" "sha1-086gTWsBeyiUrWkED+yLYj60vVI="
  "resolved" "https://registry.npm.taobao.org/ajv/download/ajv-6.10.2.tgz"
  "version" "6.10.2"
  dependencies:
    "fast-deep-equal" "^2.0.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.0":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-colors@^3.0.0":
  "integrity" "sha1-46PaS/uubIapwoViXeEkojQCb78="
  "resolved" "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-3.2.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-colors%2Fdownload%2Fansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-escapes@^1.0.0":
  "integrity" "sha1-06ioOzGapneTZisT52HHkRQiMG4="
  "resolved" "https://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-1.4.0.tgz"
  "version" "1.4.0"

"ansi-escapes@^3.0.0":
  "integrity" "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s="
  "resolved" "https://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-escapes@^4.2.1":
  "integrity" "sha1-pM4rM9ayFLeVDYWVwhLxKsnMVp0="
  "resolved" "https://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "type-fest" "^0.8.1"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://registry.npm.taobao.org/ansi-html/download/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-regex@^4.0.0":
  "integrity" "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-regex@^4.1.0":
  "integrity" "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-regex@^5.0.0":
  "integrity" "sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-5.0.0.tgz"
  "version" "5.0.0"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz?cache=0&sync_timestamp=1573584933297&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-styles%2Fdownload%2Fansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1573584933297&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha1-VoHw3PeuWICnhB2IMcRyTtnMAXI="
  "resolved" "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-4.2.0.tgz?cache=0&sync_timestamp=1573584933297&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-styles%2Fdownload%2Fansi-styles-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "@types/color-name" "^1.1.1"
    "color-convert" "^2.0.1"

"any-promise@^1.0.0":
  "integrity" "sha1-q8av7tzqUugJzcA3au0845Y10X8="
  "resolved" "https://registry.npm.taobao.org/any-promise/download/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "https://registry.npm.taobao.org/anymatch/download/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.0":
  "integrity" "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"append-transform@^0.4.0":
  "integrity" "sha1-126/jKlNJ24keja61EpLdKthGZE="
  "resolved" "https://registry.npm.taobao.org/append-transform/download/append-transform-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "default-require-extensions" "^1.0.0"

"aproba@^1.1.1":
  "integrity" "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="
  "resolved" "https://registry.npm.taobao.org/aproba/download/aproba-1.2.0.tgz"
  "version" "1.2.0"

"arch@^2.1.1", "arch@2.1.1":
  "integrity" "sha1-j1wnMao1owkpIhuwZA7tZRdeyE4="
  "resolved" "https://registry.npm.taobao.org/arch/download/arch-2.1.1.tgz"
  "version" "2.1.1"

"area-data@^5.0.6", "area-data@>=5.0.6":
  "integrity" "sha512-QxLoA+823xXKyhw5S3750I9TToki0OS42HU9ol3rCOsCXfkjtl8RtQ/eoj0cK0Levn4//oEM05FmMumfw/HIlg=="
  "resolved" "https://registry.npmjs.org/area-data/-/area-data-5.0.6.tgz"
  "version" "5.0.6"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "https://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^2.0.0":
  "integrity" "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8="
  "resolved" "https://registry.npm.taobao.org/arr-diff/download/arr-diff-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "arr-flatten" "^1.0.1"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.0.1", "arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "https://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-equal@^1.0.0":
  "integrity" "sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM="
  "resolved" "https://registry.npm.taobao.org/array-equal/download/array-equal-1.0.0.tgz"
  "version" "1.0.0"

"array-flatten@^2.1.0":
  "integrity" "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="
  "resolved" "https://registry.npm.taobao.org/array-flatten/download/array-flatten-2.1.2.tgz?cache=0&sync_timestamp=1574313293899&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-flatten%2Fdownload%2Farray-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz?cache=0&sync_timestamp=1574313293899&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-flatten%2Fdownload%2Farray-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-includes@^3.0.3":
  "integrity" "sha1-GEtI9i2S10UrsxsyMWXH+L0CJm0="
  "resolved" "https://registry.npm.taobao.org/array-includes/download/array-includes-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "define-properties" "^1.1.2"
    "es-abstract" "^1.7.0"

"array-union@^1.0.1", "array-union@^1.0.2":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.2.1":
  "integrity" "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="
  "resolved" "https://registry.npm.taobao.org/array-unique/download/array-unique-0.2.1.tgz"
  "version" "0.2.1"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"arrify@^1.0.1":
  "integrity" "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0="
  "resolved" "https://registry.npm.taobao.org/arrify/download/arrify-1.0.1.tgz"
  "version" "1.0.1"

"asn1.js@^4.0.0":
  "integrity" "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA="
  "resolved" "https://registry.npm.taobao.org/asn1.js/download/asn1.js-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"asn1@~0.2.3":
  "integrity" "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY="
  "resolved" "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs="
  "resolved" "https://registry.npm.taobao.org/assert/download/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^1.0.0":
  "integrity" "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k="
  "resolved" "https://registry.npm.taobao.org/astral-regex/download/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"async-each@^1.0.1":
  "integrity" "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="
  "resolved" "https://registry.npm.taobao.org/async-each/download/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@~1.0.0":
  "integrity" "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0="
  "resolved" "https://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-limiter%2Fdownload%2Fasync-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async-validator@~1.8.1":
  "integrity" "sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A="
  "resolved" "https://registry.npm.taobao.org/async-validator/download/async-validator-1.8.5.tgz?cache=0&sync_timestamp=1575620555389&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-validator%2Fdownload%2Fasync-validator-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "babel-runtime" "6.x"

"async@^1.5.2":
  "integrity" "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="
  "resolved" "https://registry.npm.taobao.org/async/download/async-1.5.2.tgz"
  "version" "1.5.2"

"async@^2.1.4":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"async@^2.6.2":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"async@2.6.1":
  "integrity" "sha1-skWiPKcZMAROxT+kaqAKPofGphA="
  "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "lodash" "^4.17.10"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.1":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "https://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^9.5.1":
  "integrity" "sha1-/ULtA/U96b60yg1h+09yaKm7ULQ="
  "resolved" "https://registry.npm.taobao.org/autoprefixer/download/autoprefixer-9.7.3.tgz"
  "version" "9.7.3"
  dependencies:
    "browserslist" "^4.8.0"
    "caniuse-lite" "^1.0.30001012"
    "chalk" "^2.4.2"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^7.0.23"
    "postcss-value-parser" "^4.0.2"

"aws-sign2@~0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha1-JDkOatYThrCnRyZXVNKhchnehiw="
  "resolved" "https://registry.npm.taobao.org/aws4/download/aws4-1.9.0.tgz"
  "version" "1.9.0"

"axios@^0.19.0":
  "integrity" "sha1-jgm/89kSLhM/e4EByPvdAO09Krg="
  "resolved" "https://registry.npm.taobao.org/axios/download/axios-0.19.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faxios%2Fdownload%2Faxios-0.19.0.tgz"
  "version" "0.19.0"
  dependencies:
    "follow-redirects" "1.5.10"
    "is-buffer" "^2.0.2"

"babel-code-frame@^6.22.0", "babel-code-frame@^6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "https://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "^1.1.3"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.2"

"babel-core@^6.0.0 || ^7.0.0-0", "babel-core@^6.25.0 || ^7.0.0-0", "babel-core@^7.0.0-bridge.0":
  "integrity" "sha1-laSS3dkPm06aSh2hTrM1uHtjTs4="
  "resolved" "https://registry.npm.taobao.org/babel-core/download/babel-core-7.0.0-bridge.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-core%2Fdownload%2Fbabel-core-7.0.0-bridge.0.tgz"
  "version" "7.0.0-bridge.0"

"babel-core@^6.0.0":
  "integrity" "sha1-suLwnjQtDwyI4vAuBneUEl51wgc="
  "resolved" "https://registry.npm.taobao.org/babel-core/download/babel-core-6.26.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-core%2Fdownload%2Fbabel-core-6.26.3.tgz"
  "version" "6.26.3"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-generator" "^6.26.0"
    "babel-helpers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-register" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "convert-source-map" "^1.5.1"
    "debug" "^2.6.9"
    "json5" "^0.5.1"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.4"
    "path-is-absolute" "^1.0.1"
    "private" "^0.1.8"
    "slash" "^1.0.0"
    "source-map" "^0.5.7"

"babel-core@^6.26.0":
  "integrity" "sha1-suLwnjQtDwyI4vAuBneUEl51wgc="
  "resolved" "https://registry.npm.taobao.org/babel-core/download/babel-core-6.26.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-core%2Fdownload%2Fbabel-core-6.26.3.tgz"
  "version" "6.26.3"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-generator" "^6.26.0"
    "babel-helpers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-register" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "convert-source-map" "^1.5.1"
    "debug" "^2.6.9"
    "json5" "^0.5.1"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.4"
    "path-is-absolute" "^1.0.1"
    "private" "^0.1.8"
    "slash" "^1.0.0"
    "source-map" "^0.5.7"

"babel-eslint@^10.0.1", "babel-eslint@^10.0.3":
  "integrity" "sha1-gaLGab4PIF4ZRi/tJILTPkaHqIo="
  "resolved" "https://registry.npm.taobao.org/babel-eslint/download/babel-eslint-10.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-eslint%2Fdownload%2Fbabel-eslint-10.0.3.tgz"
  "version" "10.0.3"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "eslint-visitor-keys" "^1.0.0"
    "resolve" "^1.12.0"

"babel-extract-comments@^1.0.0":
  "integrity" "sha1-Cirt+BQX7TkbheGLRhTmk6A1GiE="
  "resolved" "https://registry.npm.taobao.org/babel-extract-comments/download/babel-extract-comments-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "babylon" "^6.18.0"

"babel-generator@^6.18.0", "babel-generator@^6.26.0":
  "integrity" "sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA="
  "resolved" "https://registry.npm.taobao.org/babel-generator/download/babel-generator-6.26.1.tgz"
  "version" "6.26.1"
  dependencies:
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "detect-indent" "^4.0.0"
    "jsesc" "^1.3.0"
    "lodash" "^4.17.4"
    "source-map" "^0.5.7"
    "trim-right" "^1.0.1"

"babel-helper-vue-jsx-merge-props@^2.0.0":
  "integrity" "sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY="
  "resolved" "https://registry.npm.taobao.org/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-helpers@^6.24.1":
  "integrity" "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI="
  "resolved" "https://registry.npm.taobao.org/babel-helpers/download/babel-helpers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-jest@^23.6.0":
  "integrity" "sha1-pkQjI2ZVeiJAoMCD2msleGGFovE="
  "resolved" "https://registry.npm.taobao.org/babel-jest/download/babel-jest-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-plugin-istanbul" "^4.1.6"
    "babel-preset-jest" "^23.2.0"

"babel-jest@^24.9.0":
  "integrity" "sha1-P8Mny4RnuJ0U17xw4xUQSng8zVQ="
  "resolved" "https://registry.npm.taobao.org/babel-jest/download/babel-jest-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/babel__core" "^7.1.0"
    "babel-plugin-istanbul" "^5.1.0"
    "babel-preset-jest" "^24.9.0"
    "chalk" "^2.4.2"
    "slash" "^2.0.0"

"babel-loader@^8.0.5":
  "integrity" "sha1-4zvbbzYrA/S7FBoMIauHxQG3Dfs="
  "resolved" "https://registry.npm.taobao.org/babel-loader/download/babel-loader-8.0.6.tgz"
  "version" "8.0.6"
  dependencies:
    "find-cache-dir" "^2.0.0"
    "loader-utils" "^1.0.2"
    "mkdirp" "^0.5.1"
    "pify" "^4.0.1"

"babel-messages@^6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "https://registry.npm.taobao.org/babel-messages/download/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-dynamic-import-node@^2.2.0", "babel-plugin-dynamic-import-node@^2.3.0":
  "integrity" "sha1-8A9Qe9qjw+P/bn5emNkKesq5b38="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-dynamic-import-node%2Fdownload%2Fbabel-plugin-dynamic-import-node-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-istanbul@^4.1.6":
  "integrity" "sha1-NsWbIZLvzoHFs3gyG3QXWt0cmkU="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-istanbul/download/babel-plugin-istanbul-4.1.6.tgz?cache=0&sync_timestamp=1570587696477&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-istanbul%2Fdownload%2Fbabel-plugin-istanbul-4.1.6.tgz"
  "version" "4.1.6"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "^6.13.0"
    "find-up" "^2.1.0"
    "istanbul-lib-instrument" "^1.10.1"
    "test-exclude" "^4.2.1"

"babel-plugin-istanbul@^5.1.0":
  "integrity" "sha1-30reg9iXqS3wacTZolzyZxKTyFQ="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-istanbul/download/babel-plugin-istanbul-5.2.0.tgz?cache=0&sync_timestamp=1570587696477&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-istanbul%2Fdownload%2Fbabel-plugin-istanbul-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "find-up" "^3.0.0"
    "istanbul-lib-instrument" "^3.3.0"
    "test-exclude" "^5.2.3"

"babel-plugin-jest-hoist@^23.2.0":
  "integrity" "sha1-5h+uBaHKiAGq3uV6bWa4zvr0QWc="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-23.2.0.tgz"
  "version" "23.2.0"

"babel-plugin-jest-hoist@^24.9.0":
  "integrity" "sha1-T4NwketAfgFEfIhDy+xUbQAC11Y="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@types/babel__traverse" "^7.0.6"

"babel-plugin-module-resolver@3.2.0":
  "integrity" "sha1-***************************="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-module-resolver/download/babel-plugin-module-resolver-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "find-babel-config" "^1.1.0"
    "glob" "^7.1.2"
    "pkg-up" "^2.0.0"
    "reselect" "^3.0.1"
    "resolve" "^1.4.0"

"babel-plugin-syntax-object-rest-spread@^6.13.0", "babel-plugin-syntax-object-rest-spread@^6.8.0":
  "integrity" "sha1-/WU28rzhODb/o6VFjEkDpZe7O/U="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-transform-es2015-modules-commonjs@^6.26.0", "babel-plugin-transform-es2015-modules-commonjs@^6.26.2":
  "integrity" "sha1-WKeThjqefKhwvcWogRF/+sJ9tvM="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  "version" "6.26.2"
  dependencies:
    "babel-plugin-transform-strict-mode" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-types" "^6.26.0"

"babel-plugin-transform-object-rest-spread@^6.26.0":
  "integrity" "sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "^6.8.0"
    "babel-runtime" "^6.26.0"

"babel-plugin-transform-strict-mode@^6.24.1":
  "integrity" "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g="
  "resolved" "https://registry.npm.taobao.org/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-preset-jest@^23.2.0":
  "integrity" "sha1-jsegOhOPABoaj7HoETZSvxpV2kY="
  "resolved" "https://registry.npm.taobao.org/babel-preset-jest/download/babel-preset-jest-23.2.0.tgz"
  "version" "23.2.0"
  dependencies:
    "babel-plugin-jest-hoist" "^23.2.0"
    "babel-plugin-syntax-object-rest-spread" "^6.13.0"

"babel-preset-jest@^24.9.0":
  "integrity" "sha1-GStSHiIX+x0fZ89z9wwzZlCtPNw="
  "resolved" "https://registry.npm.taobao.org/babel-preset-jest/download/babel-preset-jest-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "babel-plugin-jest-hoist" "^24.9.0"

"babel-register@^6.26.0":
  "integrity" "sha1-btAhFz4vy0htestFxgCahW9kcHE="
  "resolved" "https://registry.npm.taobao.org/babel-register/download/babel-register-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-core" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "home-or-tmp" "^2.0.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "source-map-support" "^0.4.15"

"babel-runtime@^6.22.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-template@^6.16.0", "babel-template@^6.24.1", "babel-template@^6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "https://registry.npm.taobao.org/babel-template/download/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "lodash" "^4.17.4"

"babel-traverse@^6.0.0", "babel-traverse@^6.18.0", "babel-traverse@^6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "https://registry.npm.taobao.org/babel-traverse/download/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "debug" "^2.6.8"
    "globals" "^9.18.0"
    "invariant" "^2.2.2"
    "lodash" "^4.17.4"

"babel-types@^6.0.0", "babel-types@^6.18.0", "babel-types@^6.24.1", "babel-types@^6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "https://registry.npm.taobao.org/babel-types/download/babel-types-6.26.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-types%2Fdownload%2Fbabel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "esutils" "^2.0.2"
    "lodash" "^4.17.4"
    "to-fast-properties" "^1.0.3"

"babylon@^6.18.0":
  "integrity" "sha1-ry87iPpvXB5MY00aD46sT1WzleM="
  "resolved" "https://registry.npm.taobao.org/babylon/download/babylon-6.18.0.tgz"
  "version" "6.18.0"

"balanced-match@^1.0.0":
  "integrity" "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="
  "resolved" "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz"
  "version" "1.0.0"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.0.2":
  "integrity" "sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE="
  "resolved" "https://registry.npm.taobao.org/base64-js/download/base64-js-1.3.1.tgz"
  "version" "1.3.1"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.npm.taobao.org/batch/download/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"bfj@^6.1.1":
  "integrity" "sha1-MlyGGoIryzWKQceKM7jm4ght3n8="
  "resolved" "https://registry.npm.taobao.org/bfj/download/bfj-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "bluebird" "^3.5.5"
    "check-types" "^8.0.3"
    "hoopy" "^0.1.4"
    "tryer" "^1.0.1"

"big.js@^3.1.3":
  "integrity" "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4="
  "resolved" "https://registry.npm.taobao.org/big.js/download/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="
  "resolved" "https://registry.npm.taobao.org/big.js/download/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="
  "resolved" "https://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"bluebird@^3.1.1", "bluebird@^3.5.1", "bluebird@^3.5.5":
  "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
  "resolved" "https://registry.npm.taobao.org/bluebird/download/bluebird-3.7.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbluebird%2Fdownload%2Fbluebird-3.7.2.tgz"
  "version" "3.7.2"

"bluebird@3.5.0":
  "integrity" "sha1-eRQg1/VR7qKJdFOop3ZT+WYG1nw="
  "resolved" "https://registry.npm.taobao.org/bluebird/download/bluebird-3.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbluebird%2Fdownload%2Fbluebird-3.5.0.tgz"
  "version" "3.5.0"

"bn.js@^4.0.0", "bn.js@^4.1.0", "bn.js@^4.1.1", "bn.js@^4.4.0":
  "integrity" "sha1-LN4J617jQfSEdGuwMJsyU7GxRC8="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.11.8.tgz"
  "version" "4.11.8"

"body-parser@1.19.0":
  "integrity" "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io="
  "resolved" "https://registry.npm.taobao.org/body-parser/download/body-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "bytes" "3.1.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.7.0"
    "raw-body" "2.4.0"
    "type-is" "~1.6.17"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npm.taobao.org/bonjour/download/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npm.taobao.org/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^1.8.2":
  "integrity" "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "expand-range" "^1.8.1"
    "preserve" "^0.2.0"
    "repeat-element" "^1.1.2"

"braces@^2.3.1", "braces@^2.3.2":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"brorand@^1.0.1":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browser-process-hrtime@^0.1.2":
  "integrity" "sha1-YW8A+u8d9+wbW/nP4r3DFw8mx7Q="
  "resolved" "https://registry.npm.taobao.org/browser-process-hrtime/download/browser-process-hrtime-0.1.3.tgz"
  "version" "0.1.3"

"browser-resolve@^1.11.3":
  "integrity" "sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY="
  "resolved" "https://registry.npm.taobao.org/browser-resolve/download/browser-resolve-1.11.3.tgz"
  "version" "1.11.3"
  dependencies:
    "resolve" "1.1.7"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="
  "resolved" "https://registry.npm.taobao.org/browserify-aes/download/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="
  "resolved" "https://registry.npm.taobao.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="
  "resolved" "https://registry.npm.taobao.org/browserify-des/download/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0":
  "integrity" "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ="
  "resolved" "https://registry.npm.taobao.org/browserify-rsa/download/browserify-rsa-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.1.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg="
  "resolved" "https://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.1"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.2"
    "elliptic" "^6.0.0"
    "inherits" "^2.0.1"
    "parse-asn1" "^5.0.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="
  "resolved" "https://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.0.0", "browserslist@^4.3.4", "browserslist@^4.5.4", "browserslist@^4.8.0":
  "integrity" "sha1-tFcgrV+8hxO3JTwgdm9wHJppQok="
  "resolved" "https://registry.npm.taobao.org/browserslist/download/browserslist-4.8.2.tgz"
  "version" "4.8.2"
  dependencies:
    "caniuse-lite" "^1.0.30001015"
    "electron-to-chromium" "^1.3.322"
    "node-releases" "^1.1.42"

"bs-logger@0.x":
  "integrity" "sha1-6302UwenLPl0zGzadraDVK0za9g="
  "resolved" "https://registry.npm.taobao.org/bs-logger/download/bs-logger-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "fast-json-stable-stringify" "2.x"

"bser@^2.0.0":
  "integrity" "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU="
  "resolved" "https://registry.npm.taobao.org/bser/download/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-crc32@~0.2.3":
  "integrity" "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="
  "resolved" "https://registry.npm.taobao.org/buffer-crc32/download/buffer-crc32-0.2.13.tgz"
  "version" "0.2.13"

"buffer-from@^1.0.0", "buffer-from@1.x":
  "integrity" "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="
  "resolved" "https://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-indexof@^1.0.0":
  "integrity" "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="
  "resolved" "https://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg="
  "resolved" "https://registry.npm.taobao.org/buffer/download/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-modules@^1.1.1":
  "integrity" "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8="
  "resolved" "https://registry.npm.taobao.org/builtin-modules/download/builtin-modules-1.1.1.tgz"
  "version" "1.1.1"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.0":
  "integrity" "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="
  "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbytes%2Fdownload%2Fbytes-3.1.0.tgz"
  "version" "3.1.0"

"cacache@^10.0.4":
  "integrity" "sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA="
  "resolved" "https://registry.npm.taobao.org/cacache/download/cacache-10.0.4.tgz"
  "version" "10.0.4"
  dependencies:
    "bluebird" "^3.5.1"
    "chownr" "^1.0.1"
    "glob" "^7.1.2"
    "graceful-fs" "^4.1.11"
    "lru-cache" "^4.1.1"
    "mississippi" "^2.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.2"
    "ssri" "^5.2.4"
    "unique-filename" "^1.1.0"
    "y18n" "^4.0.0"

"cacache@^12.0.2":
  "integrity" "sha1-vpmruk4b9d9GHNWiwQcfxDJXM5A="
  "resolved" "https://registry.npm.taobao.org/cacache/download/cacache-12.0.3.tgz"
  "version" "12.0.3"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "https://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cache-loader@^2.0.1":
  "integrity" "sha1-V1j0GmLXwjlB48PHAW5vrrA6ywc="
  "resolved" "https://registry.npm.taobao.org/cache-loader/download/cache-loader-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "loader-utils" "^1.1.0"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.0"
    "normalize-path" "^3.0.0"
    "schema-utils" "^1.0.0"

"cachedir@1.3.0":
  "integrity" "sha1-XgGSi/LZW17dlLCUIYgkZ0Dg28Q="
  "resolved" "https://registry.npm.taobao.org/cachedir/download/cachedir-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "os-homedir" "^1.0.1"

"call-me-maybe@^1.0.1":
  "integrity" "sha1-JtII6onje1y95gJQoV8DHBak1ms="
  "resolved" "https://registry.npm.taobao.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
  "version" "1.0.1"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://registry.npm.taobao.org/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1562668966653&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^0.1.0":
  "integrity" "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8="
  "resolved" "https://registry.npm.taobao.org/caller-path/download/caller-path-0.1.0.tgz?cache=0&sync_timestamp=1574395542397&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaller-path%2Fdownload%2Fcaller-path-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "callsites" "^0.2.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://registry.npm.taobao.org/caller-path/download/caller-path-2.0.0.tgz?cache=0&sync_timestamp=1574395542397&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaller-path%2Fdownload%2Fcaller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^0.2.0":
  "integrity" "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo="
  "resolved" "https://registry.npm.taobao.org/callsites/download/callsites-0.2.0.tgz"
  "version" "0.2.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://registry.npm.taobao.org/callsites/download/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "https://registry.npm.taobao.org/callsites/download/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1575601622775&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase@^4.1.0":
  "integrity" "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-4.1.0.tgz"
  "version" "4.1.0"

"camelcase@^5.0.0", "camelcase@^5.2.0", "camelcase@^5.3.1":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"caniuse-api@^3.0.0":
  "integrity" "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA="
  "resolved" "https://registry.npm.taobao.org/caniuse-api/download/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001012", "caniuse-lite@^1.0.30001015":
  "integrity" "sha1-Fafd9mq6eGpx2ZYmvI8rkcbw9fA="
  "resolved" "https://registry.npm.taobao.org/caniuse-lite/download/caniuse-lite-1.0.30001015.tgz?cache=0&sync_timestamp=1575445388179&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001015.tgz"
  "version" "1.0.30001015"

"capture-exit@^1.2.0":
  "integrity" "sha1-HF/MSJ/QqwDU8ax64QcuMXP7q28="
  "resolved" "https://registry.npm.taobao.org/capture-exit/download/capture-exit-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "rsvp" "^3.3.3"

"capture-exit@^2.0.0":
  "integrity" "sha1-+5U7+uvreB9iiYI52rtCbQilCaQ="
  "resolved" "https://registry.npm.taobao.org/capture-exit/download/capture-exit-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "rsvp" "^4.8.4"

"case-sensitive-paths-webpack-plugin@^2.2.0":
  "integrity" "sha1-M3HvY2XvnCX6S4HBas4OnH3FjD4="
  "resolved" "https://registry.npm.taobao.org/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.2.0.tgz"
  "version" "2.2.0"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chalk@^1.0.0", "chalk@^1.1.1", "chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0", "chalk@^2.0.1", "chalk@^2.1.0", "chalk@^2.3.0", "chalk@^2.4.1", "chalk@^2.4.2", "chalk@2.4.2":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npm.taobao.org/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ="
  "resolved" "https://registry.npm.taobao.org/chalk/download/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chardet@^0.4.0":
  "integrity" "sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I="
  "resolved" "https://registry.npm.taobao.org/chardet/download/chardet-0.4.2.tgz"
  "version" "0.4.2"

"chardet@^0.7.0":
  "integrity" "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="
  "resolved" "https://registry.npm.taobao.org/chardet/download/chardet-0.7.0.tgz"
  "version" "0.7.0"

"charenc@0.0.2":
  "integrity" "sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc="
  "resolved" "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz"
  "version" "0.0.2"

"check-more-types@2.24.0":
  "integrity" "sha1-FCD/sQ/URNz8ebQ4kbv//TKoRgA="
  "resolved" "https://registry.npm.taobao.org/check-more-types/download/check-more-types-2.24.0.tgz"
  "version" "2.24.0"

"check-types@^8.0.3":
  "integrity" "sha1-M1bMoZyIlUTy16le1JzlCKDs9VI="
  "resolved" "https://registry.npm.taobao.org/check-types/download/check-types-8.0.3.tgz"
  "version" "8.0.3"

"chokidar@^2.0.2", "chokidar@^2.0.4", "chokidar@^2.1.8", "chokidar@>=2.0.0 <4.0.0":
  "integrity" "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc="
  "resolved" "https://registry.npm.taobao.org/chokidar/download/chokidar-2.1.8.tgz?cache=0&sync_timestamp=1572685227844&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chownr@^1.0.1", "chownr@^1.1.1":
  "integrity" "sha1-Qtg31SOWiNVfMDADpQgjD6ZycUI="
  "resolved" "https://registry.npm.taobao.org/chownr/download/chownr-1.1.3.tgz"
  "version" "1.1.3"

"chrome-trace-event@^1.0.2":
  "integrity" "sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ="
  "resolved" "https://registry.npm.taobao.org/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tslib" "^1.9.0"

"ci-info@^1.5.0":
  "integrity" "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc="
  "resolved" "https://registry.npm.taobao.org/ci-info/download/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"ci-info@^2.0.0":
  "integrity" "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y="
  "resolved" "https://registry.npm.taobao.org/ci-info/download/ci-info-2.0.0.tgz"
  "version" "2.0.0"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="
  "resolved" "https://registry.npm.taobao.org/cipher-base/download/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"circular-json@^0.3.1":
  "integrity" "sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY="
  "resolved" "https://registry.npm.taobao.org/circular-json/download/circular-json-0.3.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcircular-json%2Fdownload%2Fcircular-json-0.3.3.tgz"
  "version" "0.3.3"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "https://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha1-LUEe92uFabbQyEBo2r6FsKpeXBc="
  "resolved" "https://registry.npm.taobao.org/clean-css/download/clean-css-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "source-map" "~0.6.0"

"cli-cursor@^1.0.2":
  "integrity" "sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc="
  "resolved" "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "restore-cursor" "^1.0.1"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc="
  "resolved" "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-highlight@^2.1.0":
  "integrity" "sha1-CYy2Qs8X9CrcHBFF4H+WDsTXUis="
  "resolved" "https://registry.npm.taobao.org/cli-highlight/download/cli-highlight-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "chalk" "^3.0.0"
    "highlight.js" "^9.6.0"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^5.1.1"
    "yargs" "^15.0.0"

"cli-spinners@^0.1.2":
  "integrity" "sha1-u3ZNiOGF+54eaiofGXcjGPYF4xw="
  "resolved" "https://registry.npm.taobao.org/cli-spinners/download/cli-spinners-0.1.2.tgz"
  "version" "0.1.2"

"cli-spinners@^2.0.0":
  "integrity" "sha1-6LmI2SBsaSMC2O6DTnqFwBRNj3c="
  "resolved" "https://registry.npm.taobao.org/cli-spinners/download/cli-spinners-2.2.0.tgz"
  "version" "2.2.0"

"cli-truncate@^0.2.1":
  "integrity" "sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ="
  "resolved" "https://registry.npm.taobao.org/cli-truncate/download/cli-truncate-0.2.1.tgz?cache=0&sync_timestamp=1575618492411&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-truncate%2Fdownload%2Fcli-truncate-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "slice-ansi" "0.0.4"
    "string-width" "^1.0.1"

"cli-width@^2.0.0":
  "integrity" "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk="
  "resolved" "https://registry.npm.taobao.org/cli-width/download/cli-width-2.2.0.tgz"
  "version" "2.2.0"

"clipboardy@^2.0.0":
  "integrity" "sha1-ASOgyPrJLyVtxWM14LuL6XpJCaU="
  "resolved" "https://registry.npm.taobao.org/clipboardy/download/clipboardy-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"

"cliui@^4.0.0":
  "integrity" "sha1-NIQi2+gtgAswIu709qwQvy5NG0k="
  "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "string-width" "^2.1.1"
    "strip-ansi" "^4.0.0"
    "wrap-ansi" "^2.0.0"

"cliui@^5.0.0":
  "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
  "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^6.0.0":
  "integrity" "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE="
  "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"clone-deep@^4.0.1":
  "integrity" "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c="
  "resolved" "https://registry.npm.taobao.org/clone-deep/download/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@2.x":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npm.taobao.org/clone/download/clone-2.1.2.tgz"
  "version" "2.1.2"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://registry.npm.taobao.org/co/download/co-4.6.0.tgz"
  "version" "4.6.0"

"coa@^2.0.2":
  "integrity" "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM="
  "resolved" "https://registry.npm.taobao.org/coa/download/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "https://registry.npm.taobao.org/code-point-at/download/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0", "color-convert@^1.9.1":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz?cache=0&sync_timestamp=1566248756583&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolor-convert%2Fdownload%2Fcolor-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM="
  "resolved" "https://registry.npm.taobao.org/color-convert/download/color-convert-2.0.1.tgz?cache=0&sync_timestamp=1566248756583&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolor-convert%2Fdownload%2Fcolor-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-name@~1.1.4":
  "integrity" "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="
  "resolved" "https://registry.npm.taobao.org/color-name/download/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-string@^1.5.2":
  "integrity" "sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw="
  "resolved" "https://registry.npm.taobao.org/color-string/download/color-string-1.5.3.tgz"
  "version" "1.5.3"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^3.0.0":
  "integrity" "sha1-aBSOf4XUGtdknF+oyBBvCY0inhA="
  "resolved" "https://registry.npm.taobao.org/color/download/color-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "color-convert" "^1.9.1"
    "color-string" "^1.5.2"

"colors@^1.3.0":
  "integrity" "sha1-xQSRR51MG9rtLJztMs98fcI2D3g="
  "resolved" "https://registry.npm.taobao.org/colors/download/colors-1.4.0.tgz"
  "version" "1.4.0"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
  "resolved" "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.12.1", "commander@^2.18.0", "commander@^2.19.0", "commander@^2.20.0", "commander@~2.20.3":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1573464045808&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  "version" "2.20.3"

"commander@~2.19.0":
  "integrity" "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So="
  "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.19.0.tgz?cache=0&sync_timestamp=1573464045808&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.19.0.tgz"
  "version" "2.19.0"

"commander@2.15.1":
  "integrity" "sha1-30boZ9D8Kuxmo0ZitAapzK//Ww8="
  "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.15.1.tgz?cache=0&sync_timestamp=1573464045808&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.15.1.tgz"
  "version" "2.15.1"

"commander@2.17.x":
  "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
  "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.17.1.tgz?cache=0&sync_timestamp=1573464045808&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.17.1.tgz"
  "version" "2.17.1"

"common-tags@^1.4.0", "common-tags@1.8.0":
  "integrity" "sha1-jjFT5ULUo56bEFVENK+q+YlWqTc="
  "resolved" "https://registry.npm.taobao.org/common-tags/download/common-tags-1.8.0.tgz"
  "version" "1.8.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"compressible@~2.0.16":
  "integrity" "sha1-bowQihatWDhKl386SCyiC/8vOME="
  "resolved" "https://registry.npm.taobao.org/compressible/download/compressible-2.0.17.tgz"
  "version" "2.0.17"
  dependencies:
    "mime-db" ">= 1.40.0 < 2"

"compression@^1.7.4":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "https://registry.npm.taobao.org/compression/download/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0", "concat-stream@^1.6.0", "concat-stream@1.6.2":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"condense-newlines@^0.2.1":
  "integrity" "sha1-PemFVTE5R10yUCyDsC9gaE0kxV8="
  "resolved" "https://registry.npm.taobao.org/condense-newlines/download/condense-newlines-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-whitespace" "^0.3.0"
    "kind-of" "^3.0.2"

"config-chain@^1.1.12":
  "integrity" "sha1-D96NCRIA616AjK8l/mGMAvSOTvo="
  "resolved" "https://registry.npm.taobao.org/config-chain/download/config-chain-1.1.12.tgz"
  "version" "1.1.12"
  dependencies:
    "ini" "^1.3.4"
    "proto-list" "~1.2.1"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="
  "resolved" "https://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0":
  "integrity" "sha1-ZwY871fOts9Jk6KrOlWECujEkzY="
  "resolved" "https://registry.npm.taobao.org/console-browserify/download/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"consolidate@^0.15.1":
  "integrity" "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc="
  "resolved" "https://registry.npm.taobao.org/consolidate/download/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"contains-path@^0.1.0":
  "integrity" "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo="
  "resolved" "https://registry.npm.taobao.org/contains-path/download/contains-path-0.1.0.tgz"
  "version" "0.1.0"

"content-disposition@0.5.3":
  "integrity" "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="
  "resolved" "https://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "safe-buffer" "5.1.2"

"content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "https://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.4.0", "convert-source-map@^1.5.1", "convert-source-map@^1.7.0":
  "integrity" "sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI="
  "resolved" "https://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.7.0.tgz?cache=0&sync_timestamp=1573003762649&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.0":
  "integrity" "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="
  "resolved" "https://registry.npm.taobao.org/cookie/download/cookie-0.4.0.tgz"
  "version" "0.4.0"

"copy-concurrently@^1.0.0":
  "integrity" "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="
  "resolved" "https://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-webpack-plugin@^4.6.0":
  "integrity" "sha1-5/QN2KaEd9QF3Rt6hUquMksVi64="
  "resolved" "https://registry.npm.taobao.org/copy-webpack-plugin/download/copy-webpack-plugin-4.6.0.tgz?cache=0&sync_timestamp=1573062711769&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcopy-webpack-plugin%2Fdownload%2Fcopy-webpack-plugin-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "cacache" "^10.0.4"
    "find-cache-dir" "^1.0.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.0"
    "loader-utils" "^1.1.0"
    "minimatch" "^3.0.4"
    "p-limit" "^1.0.0"
    "serialize-javascript" "^1.4.0"

"core-js@^2.4.0", "core-js@^2.5.0", "core-js@^2.6.5":
  "integrity" "sha1-iluDkfjMcBPacDQRzltYVwYwDX8="
  "resolved" "https://registry.npm.taobao.org/core-js/download/core-js-2.6.10.tgz"
  "version" "2.6.10"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^5.0.0":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "https://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-5.2.1.tgz?cache=0&sync_timestamp=1572710682964&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"create-ecdh@^4.0.0":
  "integrity" "sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8="
  "resolved" "https://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.0.0"

"create-hash@^1.1.0", "create-hash@^1.1.2":
  "integrity" "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="
  "resolved" "https://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.2", "create-hmac@^1.1.4":
  "integrity" "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="
  "resolved" "https://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-spawn@^5.0.1":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz?cache=0&sync_timestamp=1570439982137&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^5.1.0":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz?cache=0&sync_timestamp=1570439982137&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0", "cross-spawn@^6.0.5":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz?cache=0&sync_timestamp=1570439982137&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0":
  "integrity" "sha1-CrVihuD3wk4VPQTMKqAn5DqaXRQ="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.1.tgz?cache=0&sync_timestamp=1570439982137&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypt@0.0.2":
  "integrity" "sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs="
  "resolved" "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz"
  "version" "0.0.2"

"crypto-browserify@^3.11.0":
  "integrity" "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="
  "resolved" "https://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha1-wZiUD2OnbX42wecQGLABchBUyyI="
  "resolved" "https://registry.npm.taobao.org/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-loader@*", "css-loader@^1.0.1":
  "integrity" "sha1-aIW7UjOzXsR7AGBX2gHMZAtref4="
  "resolved" "https://registry.npm.taobao.org/css-loader/download/css-loader-1.0.1.tgz?cache=0&sync_timestamp=1575295608299&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-loader%2Fdownload%2Fcss-loader-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "css-selector-tokenizer" "^0.7.0"
    "icss-utils" "^2.1.0"
    "loader-utils" "^1.0.2"
    "lodash" "^4.17.11"
    "postcss" "^6.0.23"
    "postcss-modules-extract-imports" "^1.2.0"
    "postcss-modules-local-by-default" "^1.2.0"
    "postcss-modules-scope" "^1.1.0"
    "postcss-modules-values" "^1.3.0"
    "postcss-value-parser" "^3.3.0"
    "source-list-map" "^2.0.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc="
  "resolved" "https://registry.npm.taobao.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^1.1.0":
  "integrity" "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg="
  "resolved" "https://registry.npm.taobao.org/css-select/download/css-select-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "boolbase" "~1.0.0"
    "css-what" "2.1"
    "domutils" "1.5.1"
    "nth-check" "~1.0.1"

"css-select@^2.0.0":
  "integrity" "sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="
  "resolved" "https://registry.npm.taobao.org/css-select/download/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-selector-tokenizer@^0.7.0":
  "integrity" "sha1-oXcnGovKUBkXL0+JH8bu2cv2jV0="
  "resolved" "https://registry.npm.taobao.org/css-selector-tokenizer/download/css-selector-tokenizer-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "cssesc" "^0.1.0"
    "fastparse" "^1.1.1"
    "regexpu-core" "^1.0.0"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha1-mL69YsTB2flg7DQM+fdSLjBwmiI="
  "resolved" "https://registry.npm.taobao.org/css-tree/download/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-unit-converter@^1.1.1":
  "integrity" "sha1-2bkoGtz9jO2TW9urqDeGiX9k6ZY="
  "resolved" "https://registry.npm.taobao.org/css-unit-converter/download/css-unit-converter-1.1.1.tgz"
  "version" "1.1.1"

"css-what@^3.2.1":
  "integrity" "sha1-9KjxJCEGRiG0VnVeNKA6LCLfXaE="
  "resolved" "https://registry.npm.taobao.org/css-what/download/css-what-3.2.1.tgz"
  "version" "3.2.1"

"css-what@2.1":
  "integrity" "sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI="
  "resolved" "https://registry.npm.taobao.org/css-what/download/css-what-2.1.3.tgz"
  "version" "2.1.3"

"css@^2.1.0":
  "integrity" "sha1-xkZ1XHOXHyu6amAeLPL9cbEpiSk="
  "resolved" "https://registry.npm.taobao.org/css/download/css-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "inherits" "^2.0.3"
    "source-map" "^0.6.1"
    "source-map-resolve" "^0.5.2"
    "urix" "^0.1.0"

"cssesc@^0.1.0":
  "integrity" "sha1-yBSQPkViM3GgR3tAEJqq++6t27Q="
  "resolved" "https://registry.npm.taobao.org/cssesc/download/cssesc-0.1.0.tgz"
  "version" "0.1.0"

"cssesc@^2.0.0":
  "integrity" "sha1-OxO9G7HLNuG8taTc0n9UxdyzVwM="
  "resolved" "https://registry.npm.taobao.org/cssesc/download/cssesc-2.0.0.tgz"
  "version" "2.0.0"

"cssnano-preset-default@^4.0.0", "cssnano-preset-default@^4.0.7":
  "integrity" "sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y="
  "resolved" "https://registry.npm.taobao.org/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz"
  "version" "4.0.7"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.2"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^4.0.0", "cssnano@^4.1.10":
  "integrity" "sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI="
  "resolved" "https://registry.npm.taobao.org/cssnano/download/cssnano-4.1.10.tgz"
  "version" "4.1.10"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.7"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha1-5fgas6Vrju+38Aks5yeTKfRU3j0="
  "resolved" "https://registry.npm.taobao.org/csso/download/csso-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "css-tree" "1.0.0-alpha.37"

"cssom@>= 0.3.2 < 0.4.0", "cssom@0.3.x":
  "integrity" "sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o="
  "resolved" "https://registry.npm.taobao.org/cssom/download/cssom-0.3.8.tgz?cache=0&sync_timestamp=1573719337707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssom%2Fdownload%2Fcssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^1.0.0":
  "integrity" "sha1-nTEyginTxWXGHlhrAgQaKPzNzPE="
  "resolved" "https://registry.npm.taobao.org/cssstyle/download/cssstyle-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "cssom" "0.3.x"

"current-script-polyfill@^1.0.0":
  "integrity" "sha1-8xz35PPiGLBybnOMqSoC00iO9hU="
  "resolved" "https://registry.npm.taobao.org/current-script-polyfill/download/current-script-polyfill-1.0.0.tgz"
  "version" "1.0.0"

"cyclist@^1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://registry.npm.taobao.org/cyclist/download/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"cypress@^3.2.0":
  "integrity" "sha1-4s1xuHts4NTHLG6iXaEAXXXB8jE="
  "resolved" "https://registry.npm.taobao.org/cypress/download/cypress-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "@cypress/listr-verbose-renderer" "0.4.1"
    "@cypress/xvfb" "1.2.4"
    "@types/sizzle" "2.3.2"
    "arch" "2.1.1"
    "bluebird" "3.5.0"
    "cachedir" "1.3.0"
    "chalk" "2.4.2"
    "check-more-types" "2.24.0"
    "commander" "2.15.1"
    "common-tags" "1.8.0"
    "debug" "3.2.6"
    "execa" "0.10.0"
    "executable" "4.1.1"
    "extract-zip" "1.6.7"
    "fs-extra" "5.0.0"
    "getos" "3.1.1"
    "is-ci" "1.2.1"
    "is-installed-globally" "0.1.0"
    "lazy-ass" "1.6.0"
    "listr" "0.12.0"
    "lodash" "4.17.15"
    "log-symbols" "2.2.0"
    "minimist" "1.2.0"
    "moment" "2.24.0"
    "ramda" "0.24.1"
    "request" "2.88.0"
    "request-progress" "3.0.0"
    "supports-color" "5.5.0"
    "tmp" "0.1.0"
    "untildify" "3.0.3"
    "url" "0.11.0"
    "yauzl" "2.10.0"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"data-urls@^1.0.0":
  "integrity" "sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4="
  "resolved" "https://registry.npm.taobao.org/data-urls/download/data-urls-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "abab" "^2.0.0"
    "whatwg-mimetype" "^2.2.0"
    "whatwg-url" "^7.0.0"

"date-fns@^1.27.2":
  "integrity" "sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw="
  "resolved" "https://registry.npm.taobao.org/date-fns/download/date-fns-1.30.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdate-fns%2Fdownload%2Fdate-fns-1.30.1.tgz"
  "version" "1.30.1"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "https://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.2.0":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.8":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.0", "debug@=3.1.0":
  "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.1":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.5":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1":
  "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.0":
  "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.1":
  "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ms" "^2.1.1"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@3.2.6":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"decamelize@^1.1.1", "decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o="
  "resolved" "https://registry.npm.taobao.org/deep-equal/download/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@~0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "https://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"deepmerge@^1.2.0", "deepmerge@^1.5.2":
  "integrity" "sha1-EEmdhohEza1P7ghC34x/bwyVp1M="
  "resolved" "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"deepmerge@^4.2.2":
  "integrity" "sha1-RNLqNnm49NT/ujPwPYZfwee/SVU="
  "resolved" "https://registry.npm.taobao.org/deepmerge/download/deepmerge-4.2.2.tgz"
  "version" "4.2.2"

"default-gateway@^4.2.0":
  "integrity" "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs="
  "resolved" "https://registry.npm.taobao.org/default-gateway/download/default-gateway-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "execa" "^1.0.0"
    "ip-regex" "^2.1.0"

"default-gateway@^5.0.2":
  "integrity" "sha1-T9a9XShV05s0zFpZUFSG6ar8mxA="
  "resolved" "https://registry.npm.taobao.org/default-gateway/download/default-gateway-5.0.5.tgz"
  "version" "5.0.5"
  dependencies:
    "execa" "^3.3.0"

"default-require-extensions@^1.0.0":
  "integrity" "sha1-836hXT4T/9m0N9M+GnW1+5eHTLg="
  "resolved" "https://registry.npm.taobao.org/default-require-extensions/download/default-require-extensions-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "strip-bom" "^2.0.0"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "https://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.2", "define-properties@^1.1.3":
  "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
  "resolved" "https://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^4.1.1":
  "integrity" "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ="
  "resolved" "https://registry.npm.taobao.org/del/download/del-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@types/glob" "^7.1.1"
    "globby" "^6.1.0"
    "is-path-cwd" "^2.0.0"
    "is-path-in-cwd" "^2.0.0"
    "p-map" "^2.0.0"
    "pify" "^4.0.1"
    "rimraf" "^2.6.3"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@^1.0.0":
  "integrity" "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM="
  "resolved" "https://registry.npm.taobao.org/des.js/download/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-indent@^4.0.0":
  "integrity" "sha1-920GQ1LN9Docts5hnE7jqUdd4gg="
  "resolved" "https://registry.npm.taobao.org/detect-indent/download/detect-indent-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "repeating" "^2.0.0"

"detect-libc@^1.0.3":
  "integrity" "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups="
  "resolved" "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz"
  "version" "1.0.3"

"detect-newline@^2.1.0":
  "integrity" "sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I="
  "resolved" "https://registry.npm.taobao.org/detect-newline/download/detect-newline-2.1.0.tgz"
  "version" "2.1.0"

"detect-node@^2.0.4":
  "integrity" "sha1-AU7o+PZpxcWAI9pkuBecCDooxGw="
  "resolved" "https://registry.npm.taobao.org/detect-node/download/detect-node-2.0.4.tgz"
  "version" "2.0.4"

"diff-sequences@^24.9.0":
  "integrity" "sha1-VxXWJE4qpl9Iu6C8ly2wsLEelbU="
  "resolved" "https://registry.npm.taobao.org/diff-sequences/download/diff-sequences-24.9.0.tgz?cache=0&sync_timestamp=1566445327355&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdiff-sequences%2Fdownload%2Fdiff-sequences-24.9.0.tgz"
  "version" "24.9.0"

"diff@^3.2.0":
  "integrity" "sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI="
  "resolved" "https://registry.npm.taobao.org/diff/download/diff-3.5.0.tgz"
  "version" "3.5.0"

"diff@^4.0.1":
  "integrity" "sha1-DGZ8tGfru1zqfxTxNcwtuneAqP8="
  "resolved" "https://registry.npm.taobao.org/diff/download/diff-4.0.1.tgz"
  "version" "4.0.1"

"diffie-hellman@^5.0.0":
  "integrity" "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="
  "resolved" "https://registry.npm.taobao.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0", "dir-glob@^2.2.2":
  "integrity" "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ="
  "resolved" "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdir-glob%2Fdownload%2Fdir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npm.taobao.org/dns-equal/download/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo="
  "resolved" "https://registry.npm.taobao.org/dns-packet/download/dns-packet-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.npm.taobao.org/dns-txt/download/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^2.1.0":
  "integrity" "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@1.5.0":
  "integrity" "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "esutils" "^2.0.2"
    "isarray" "^1.0.0"

"dom-converter@^0.2":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "https://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-event-types@^1.0.0":
  "integrity" "sha1-WDCgop4b+Df+UKcM2ApZcjKBPK4="
  "resolved" "https://registry.npm.taobao.org/dom-event-types/download/dom-event-types-1.0.0.tgz"
  "version" "1.0.0"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "https://registry.npm.taobao.org/dom-serializer/download/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"domain-browser@^1.1.1":
  "integrity" "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="
  "resolved" "https://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomain-browser%2Fdownload%2Fdomain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^1.3.1", "domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "https://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@^2.0.1":
  "integrity" "sha1-H4vf6R9aeAYydOgDtL3O326U+U0="
  "resolved" "https://registry.npm.taobao.org/domelementtype/download/domelementtype-2.0.1.tgz"
  "version" "2.0.1"

"domexception@^1.0.1":
  "integrity" "sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA="
  "resolved" "https://registry.npm.taobao.org/domexception/download/domexception-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "webidl-conversions" "^4.0.2"

"domhandler@^2.3.0":
  "integrity" "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM="
  "resolved" "https://registry.npm.taobao.org/domhandler/download/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domutils@^1.5.1", "domutils@^1.7.0":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "https://registry.npm.taobao.org/domutils/download/domutils-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@1.5.1":
  "integrity" "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8="
  "resolved" "https://registry.npm.taobao.org/domutils/download/domutils-1.5.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"dot-prop@^4.1.1":
  "integrity" "sha1-HxngwuGqDjJ5fEl5nyg3rGr2nFc="
  "resolved" "https://registry.npm.taobao.org/dot-prop/download/dot-prop-4.2.0.tgz?cache=0&sync_timestamp=1572620572572&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-prop%2Fdownload%2Fdot-prop-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "is-obj" "^1.0.0"

"dotenv-expand@^5.1.0":
  "integrity" "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA="
  "resolved" "https://registry.npm.taobao.org/dotenv-expand/download/dotenv-expand-5.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdotenv-expand%2Fdownload%2Fdotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^7.0.0":
  "integrity" "sha1-or481Sc2ZzIG6KhftSEO6ilijnw="
  "resolved" "https://registry.npm.taobao.org/dotenv/download/dotenv-7.0.0.tgz"
  "version" "7.0.0"

"duplexer@^0.1.1":
  "integrity" "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="
  "resolved" "https://registry.npm.taobao.org/duplexer/download/duplexer-0.1.1.tgz"
  "version" "0.1.1"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk="
  "resolved" "https://registry.npm.taobao.org/duplexify/download/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"easy-stack@^1.0.0":
  "integrity" "sha1-EskbMIWjfwuqM26UhurEv5Tj54g="
  "resolved" "https://registry.npm.taobao.org/easy-stack/download/easy-stack-1.0.0.tgz"
  "version" "1.0.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"echarts@^5.3.2":
  "integrity" "sha512-LWCt7ohOKdJqyiBJ0OGBmE9szLdfA9sGcsMEi+GGoc6+Xo75C+BkcT/6NNGRHAWtnQl2fNow05AQjznpap28TQ=="
  "resolved" "https://registry.npmjs.org/echarts/-/echarts-5.3.2.tgz"
  "version" "5.3.2"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.3.1"

"editorconfig@^0.15.3":
  "integrity" "sha1-vvhMTnX7jcsM5c7o79UcFZmb78U="
  "resolved" "https://registry.npm.taobao.org/editorconfig/download/editorconfig-0.15.3.tgz"
  "version" "0.15.3"
  dependencies:
    "commander" "^2.19.0"
    "lru-cache" "^4.1.5"
    "semver" "^5.6.0"
    "sigmund" "^1.0.1"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.6.1":
  "integrity" "sha1-SGYSh1c9zFPjZsehrlLDoSDuybo="
  "resolved" "https://registry.npm.taobao.org/ejs/download/ejs-2.7.4.tgz"
  "version" "2.7.4"

"electron-to-chromium@^1.3.322":
  "integrity" "sha1-pvfhx5AlwrBYOOjjRPbonrgyE6g="
  "resolved" "https://registry.npm.taobao.org/electron-to-chromium/download/electron-to-chromium-1.3.322.tgz"
  "version" "1.3.322"

"elegant-spinner@^1.0.1":
  "integrity" "sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4="
  "resolved" "https://registry.npm.taobao.org/elegant-spinner/download/elegant-spinner-1.0.1.tgz"
  "version" "1.0.1"

"element-ui@^2.12.0":
  "integrity" "sha1-9rsE5bCnbqX2JGYES3dEB7pOvS0="
  "resolved" "https://registry.npm.taobao.org/element-ui/download/element-ui-2.13.0.tgz"
  "version" "2.13.0"
  dependencies:
    "async-validator" "~1.8.1"
    "babel-helper-vue-jsx-merge-props" "^2.0.0"
    "deepmerge" "^1.2.0"
    "normalize-wheel" "^1.0.1"
    "resize-observer-polyfill" "^1.5.0"
    "throttle-debounce" "^1.0.1"

"elliptic@^6.0.0":
  "integrity" "sha1-BcVnjXFzwEnYykM1UiJKSV0ON2I="
  "resolved" "https://registry.npm.taobao.org/elliptic/download/elliptic-6.5.2.tgz"
  "version" "6.5.2"
  dependencies:
    "bn.js" "^4.4.0"
    "brorand" "^1.0.1"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.0"

"emoji-regex@^7.0.1":
  "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
  "resolved" "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "https://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^4.0.0", "enhanced-resolve@^4.1.0":
  "integrity" "sha1-KTfiuAZs0P584JkKmPDXGjUYn2Y="
  "resolved" "https://registry.npm.taobao.org/enhanced-resolve/download/enhanced-resolve-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.5.0"
    "tapable" "^1.0.0"

"entities@^1.1.1":
  "integrity" "sha1-vfpzUplmTfr9NFKe1PhSKidf6lY="
  "resolved" "https://registry.npm.taobao.org/entities/download/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@^2.0.0":
  "integrity" "sha1-aNYITKsbB5dnVA2A5Wo5tCPkq/Q="
  "resolved" "https://registry.npm.taobao.org/entities/download/entities-2.0.0.tgz"
  "version" "2.0.0"

"errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg="
  "resolved" "https://registry.npm.taobao.org/errno/download/errno-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.2.0", "error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "https://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.0":
  "integrity" "sha1-p1c5fcXZ3pc6yaXX1Oit58+ukQE="
  "resolved" "https://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "stackframe" "^1.1.0"

"es-abstract@^1.12.0", "es-abstract@^1.4.3", "es-abstract@^1.5.1", "es-abstract@^1.7.0":
  "integrity" "sha1-UkkNl4+W/5+J7BW1zyRDBKW8oWE="
  "resolved" "https://registry.npm.taobao.org/es-abstract/download/es-abstract-1.16.3.tgz?cache=0&sync_timestamp=1575490129976&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fes-abstract%2Fdownload%2Fes-abstract-1.16.3.tgz"
  "version" "1.16.3"
  dependencies:
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"
    "is-callable" "^1.1.4"
    "is-regex" "^1.0.4"
    "object-inspect" "^1.7.0"
    "object-keys" "^1.1.1"
    "string.prototype.trimleft" "^2.1.0"
    "string.prototype.trimright" "^2.1.0"

"es-to-primitive@^1.2.1":
  "integrity" "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="
  "resolved" "https://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.1.tgz?cache=0&sync_timestamp=1573280919382&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fes-to-primitive%2Fdownload%2Fes-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escodegen@^1.9.1":
  "integrity" "sha1-92Pa+ECvFyuzorbdchnA4X9/9UE="
  "resolved" "https://registry.npm.taobao.org/escodegen/download/escodegen-1.12.0.tgz"
  "version" "1.12.0"
  dependencies:
    "esprima" "^3.1.3"
    "estraverse" "^4.2.0"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-config-standard@^12.0.0":
  "integrity" "sha1-Y4tMZdsL1aQTGflruh8V3a0hB9k="
  "resolved" "https://registry.npm.taobao.org/eslint-config-standard/download/eslint-config-standard-12.0.0.tgz"
  "version" "12.0.0"

"eslint-import-resolver-node@^0.3.2":
  "integrity" "sha1-WPFfuDm40FdsqYBBNHaqskcttmo="
  "resolved" "https://registry.npm.taobao.org/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "debug" "^2.6.9"
    "resolve" "^1.5.0"

"eslint-loader@^2.1.2":
  "integrity" "sha1-KLnBLaVAV68IReKmEScBova/gzc="
  "resolved" "https://registry.npm.taobao.org/eslint-loader/download/eslint-loader-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-module-utils@^2.4.0":
  "integrity" "sha1-e0Z1h1v5aw2/GyGXdFblux9eAYw="
  "resolved" "https://registry.npm.taobao.org/eslint-module-utils/download/eslint-module-utils-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "debug" "^2.6.8"
    "pkg-dir" "^2.0.0"

"eslint-plugin-cypress@^2.2.1":
  "integrity" "sha1-EX8UzmNpjkxPOv6j1+JwJcjVBPA="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-cypress/download/eslint-plugin-cypress-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "globals" "^11.12.0"

"eslint-plugin-es@^1.3.1":
  "integrity" "sha1-EqyuD0lT52ukRL/RsicQgaxiCZg="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "eslint-utils" "^1.4.2"
    "regexpp" "^2.0.1"

"eslint-plugin-import@^2.14.0", "eslint-plugin-import@>=2.13.0":
  "integrity" "sha1-AvEYC5Cwd7M9RHoXojJs60AKzrY="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-import/download/eslint-plugin-import-2.18.2.tgz"
  "version" "2.18.2"
  dependencies:
    "array-includes" "^3.0.3"
    "contains-path" "^0.1.0"
    "debug" "^2.6.9"
    "doctrine" "1.5.0"
    "eslint-import-resolver-node" "^0.3.2"
    "eslint-module-utils" "^2.4.0"
    "has" "^1.0.3"
    "minimatch" "^3.0.4"
    "object.values" "^1.1.0"
    "read-pkg-up" "^2.0.0"
    "resolve" "^1.11.0"

"eslint-plugin-node@^8.0.0", "eslint-plugin-node@>=7.0.0":
  "integrity" "sha1-Va41YAIoY9FB+noReZUyNApoWWQ="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-node/download/eslint-plugin-node-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "eslint-plugin-es" "^1.3.1"
    "eslint-utils" "^1.3.1"
    "ignore" "^5.0.2"
    "minimatch" "^3.0.4"
    "resolve" "^1.8.1"
    "semver" "^5.5.0"

"eslint-plugin-promise@^4.0.1", "eslint-plugin-promise@>=4.0.0":
  "integrity" "sha1-hF/YsiYK2PglZMEiL85ErXHZQYo="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-promise/download/eslint-plugin-promise-4.2.1.tgz"
  "version" "4.2.1"

"eslint-plugin-standard@^4.0.0", "eslint-plugin-standard@>=4.0.0":
  "integrity" "sha1-/wUZ9/+v8RT3bRvXw5lu7w9uILQ="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-standard/download/eslint-plugin-standard-4.0.1.tgz"
  "version" "4.0.1"

"eslint-plugin-vue@^4.7.1":
  "integrity" "sha1-yCm5/GJYLBiXtaC5Sv1E7MpRHmM="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-vue/download/eslint-plugin-vue-4.7.1.tgz"
  "version" "4.7.1"
  dependencies:
    "vue-eslint-parser" "^2.0.3"

"eslint-plugin-vue@^5.2.3":
  "integrity" "sha1-PudZfYI7VHiASy/rqYY7G3QnOWE="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-vue/download/eslint-plugin-vue-5.2.3.tgz"
  "version" "5.2.3"
  dependencies:
    "vue-eslint-parser" "^5.0.0"

"eslint-scope@^3.7.1":
  "integrity" "sha1-u1ByANPRf2AkdjYWC0gmKEsQhTU="
  "resolved" "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-scope@^4.0.0", "eslint-scope@^4.0.3":
  "integrity" "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg="
  "resolved" "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-scope@^5.0.0":
  "integrity" "sha1-6HyIh8c+jR7ITxylkWRcNYv8j7k="
  "resolved" "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-utils@^1.3.1", "eslint-utils@^1.4.2", "eslint-utils@^1.4.3":
  "integrity" "sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8="
  "resolved" "https://registry.npm.taobao.org/eslint-utils/download/eslint-utils-1.4.3.tgz?cache=0&sync_timestamp=1571580338929&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-utils%2Fdownload%2Feslint-utils-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-visitor-keys@^1.0.0", "eslint-visitor-keys@^1.1.0":
  "integrity" "sha1-4qgs6oT/JGrW+1f5veW0ZiFFnsI="
  "resolved" "https://registry.npm.taobao.org/eslint-visitor-keys/download/eslint-visitor-keys-1.1.0.tgz?cache=0&sync_timestamp=1565705523991&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.1.0.tgz"
  "version" "1.1.0"

"eslint@*", "eslint@^5.0.0", "eslint@^6.2.2", "eslint@>= 3.2.1", "eslint@>= 4.12.1", "eslint@>=1.6.0 <7.0.0", "eslint@>=3.9.0", "eslint@>=4.19.1", "eslint@>=5.0.0", "eslint@2.x - 6.x":
  "integrity" "sha1-wXcHykrXstivmGoz/rpx4Yqf7NE="
  "resolved" "https://registry.npm.taobao.org/eslint/download/eslint-6.7.2.tgz?cache=0&sync_timestamp=1575161102132&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint%2Fdownload%2Feslint-6.7.2.tgz"
  "version" "6.7.2"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "ajv" "^6.10.0"
    "chalk" "^2.1.0"
    "cross-spawn" "^6.0.5"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "eslint-scope" "^5.0.0"
    "eslint-utils" "^1.4.3"
    "eslint-visitor-keys" "^1.1.0"
    "espree" "^6.1.2"
    "esquery" "^1.0.1"
    "esutils" "^2.0.2"
    "file-entry-cache" "^5.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.0.0"
    "globals" "^12.1.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "inquirer" "^7.0.0"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.3.0"
    "lodash" "^4.17.14"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.3"
    "progress" "^2.0.0"
    "regexpp" "^2.0.1"
    "semver" "^6.1.2"
    "strip-ansi" "^5.2.0"
    "strip-json-comments" "^3.0.1"
    "table" "^5.2.3"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"eslint@^3.18.0 || ^4.0.0", "eslint@^4.19.1":
  "integrity" "sha1-MtHWU+HZBAiFS/spbwdux+GGowA="
  "resolved" "https://registry.npm.taobao.org/eslint/download/eslint-4.19.1.tgz?cache=0&sync_timestamp=1575161102132&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint%2Fdownload%2Feslint-4.19.1.tgz"
  "version" "4.19.1"
  dependencies:
    "ajv" "^5.3.0"
    "babel-code-frame" "^6.22.0"
    "chalk" "^2.1.0"
    "concat-stream" "^1.6.0"
    "cross-spawn" "^5.1.0"
    "debug" "^3.1.0"
    "doctrine" "^2.1.0"
    "eslint-scope" "^3.7.1"
    "eslint-visitor-keys" "^1.0.0"
    "espree" "^3.5.4"
    "esquery" "^1.0.0"
    "esutils" "^2.0.2"
    "file-entry-cache" "^2.0.0"
    "functional-red-black-tree" "^1.0.1"
    "glob" "^7.1.2"
    "globals" "^11.0.1"
    "ignore" "^3.3.3"
    "imurmurhash" "^0.1.4"
    "inquirer" "^3.0.6"
    "is-resolvable" "^1.0.0"
    "js-yaml" "^3.9.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.3.0"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.2"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.2"
    "path-is-inside" "^1.0.2"
    "pluralize" "^7.0.0"
    "progress" "^2.0.0"
    "regexpp" "^1.0.1"
    "require-uncached" "^1.0.3"
    "semver" "^5.3.0"
    "strip-ansi" "^4.0.0"
    "strip-json-comments" "~2.0.1"
    "table" "4.0.2"
    "text-table" "~0.2.0"

"espree@^3.5.2", "espree@^3.5.4":
  "integrity" "sha1-sPRHGHyKi+2US4FaZgvd9d610ac="
  "resolved" "https://registry.npm.taobao.org/espree/download/espree-3.5.4.tgz"
  "version" "3.5.4"
  dependencies:
    "acorn" "^5.5.0"
    "acorn-jsx" "^3.0.0"

"espree@^4.1.0":
  "integrity" "sha1-co1UUeD9FWwEOEp62J7VH/VOsl8="
  "resolved" "https://registry.npm.taobao.org/espree/download/espree-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "acorn" "^6.0.2"
    "acorn-jsx" "^5.0.0"
    "eslint-visitor-keys" "^1.0.0"

"espree@^6.1.2":
  "integrity" "sha1-bCcmUJMrT5HDcU5ee19eLs9HJi0="
  "resolved" "https://registry.npm.taobao.org/espree/download/espree-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "acorn" "^7.1.0"
    "acorn-jsx" "^5.1.0"
    "eslint-visitor-keys" "^1.1.0"

"esprima@^3.1.3":
  "integrity" "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM="
  "resolved" "https://registry.npm.taobao.org/esprima/download/esprima-3.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesprima%2Fdownload%2Fesprima-3.1.3.tgz"
  "version" "3.1.3"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesprima%2Fdownload%2Fesprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.0.0", "esquery@^1.0.1":
  "integrity" "sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg="
  "resolved" "https://registry.npm.taobao.org/esquery/download/esquery-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "estraverse" "^4.0.0"

"esrecurse@^4.1.0":
  "integrity" "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8="
  "resolved" "https://registry.npm.taobao.org/esrecurse/download/esrecurse-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estraverse" "^4.1.0"

"estraverse@^4.0.0", "estraverse@^4.1.0", "estraverse@^4.1.1", "estraverse@^4.2.0":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz?cache=0&sync_timestamp=1564535520945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesutils%2Fdownload%2Fesutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-pubsub@4.3.0":
  "integrity" "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24="
  "resolved" "https://registry.npm.taobao.org/event-pubsub/download/event-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^4.0.0":
  "integrity" "sha1-1lF2FjiH7lnzhtZMgmELaWpKdOs="
  "resolved" "https://registry.npm.taobao.org/eventemitter3/download/eventemitter3-4.0.0.tgz"
  "version" "4.0.0"

"events@^3.0.0":
  "integrity" "sha1-mgoN+vYok9krh1uPJpjKQRSXPog="
  "resolved" "https://registry.npm.taobao.org/events/download/events-3.0.0.tgz"
  "version" "3.0.0"

"eventsource@^1.0.7":
  "integrity" "sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA="
  "resolved" "https://registry.npm.taobao.org/eventsource/download/eventsource-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "original" "^1.0.0"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="
  "resolved" "https://registry.npm.taobao.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"exec-sh@^0.2.0":
  "integrity" "sha1-Kl5//L19C6J1W97LFuWkJ9+97DY="
  "resolved" "https://registry.npm.taobao.org/exec-sh/download/exec-sh-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "merge" "^1.2.0"

"exec-sh@^0.3.2":
  "integrity" "sha1-OgGM61JsxvbfK7UEsr/o46STTsU="
  "resolved" "https://registry.npm.taobao.org/exec-sh/download/exec-sh-0.3.4.tgz"
  "version" "0.3.4"

"execa@^0.8.0":
  "integrity" "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-0.8.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-1.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^3.3.0":
  "integrity" "sha1-wI7UVQ72XYWPrCaf/IVyRG8364k="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-3.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@0.10.0":
  "integrity" "sha1-/0Vqj1P5D47MxxqW0Rvfx/CCy1A="
  "resolved" "https://registry.npm.taobao.org/execa/download/execa-0.10.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"executable@4.1.1":
  "integrity" "sha1-QVMr/zYdPlevTXY7cFgtsY9dEzw="
  "resolved" "https://registry.npm.taobao.org/executable/download/executable-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "pify" "^2.2.0"

"exit-hook@^1.0.0":
  "integrity" "sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g="
  "resolved" "https://registry.npm.taobao.org/exit-hook/download/exit-hook-1.1.1.tgz"
  "version" "1.1.1"

"exit@^0.1.2":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "https://registry.npm.taobao.org/exit/download/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^0.1.4":
  "integrity" "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s="
  "resolved" "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "is-posix-bracket" "^0.1.0"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expand-range@^1.8.1":
  "integrity" "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc="
  "resolved" "https://registry.npm.taobao.org/expand-range/download/expand-range-1.8.2.tgz"
  "version" "1.8.2"
  dependencies:
    "fill-range" "^2.1.0"

"expect@^23.6.0":
  "integrity" "sha1-HgyNO6mlgch71x+5vIhi1ENCX5g="
  "resolved" "https://registry.npm.taobao.org/expect/download/expect-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "jest-diff" "^23.6.0"
    "jest-get-type" "^22.1.0"
    "jest-matcher-utils" "^23.6.0"
    "jest-message-util" "^23.4.0"
    "jest-regex-util" "^23.3.0"

"expect@^24.9.0":
  "integrity" "sha1-t1FltIFwdPpKFXeU9G/p8boVtso="
  "resolved" "https://registry.npm.taobao.org/expect/download/expect-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "ansi-styles" "^3.2.0"
    "jest-get-type" "^24.9.0"
    "jest-matcher-utils" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-regex-util" "^24.9.0"

"express@^4.16.3", "express@^4.17.1":
  "integrity" "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ="
  "resolved" "https://registry.npm.taobao.org/express/download/express-4.17.1.tgz"
  "version" "4.17.1"
  dependencies:
    "accepts" "~1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.0"
    "content-disposition" "0.5.3"
    "content-type" "~1.0.4"
    "cookie" "0.4.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.5"
    "qs" "6.7.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.1.2"
    "send" "0.17.1"
    "serve-static" "1.14.1"
    "setprototypeof" "1.1.1"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@~3.0.2":
  "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
  "resolved" "https://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^2.0.4":
  "integrity" "sha1-BFURz9jRM/OEZnPRBHwVTiFK09U="
  "resolved" "https://registry.npm.taobao.org/external-editor/download/external-editor-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chardet" "^0.4.0"
    "iconv-lite" "^0.4.17"
    "tmp" "^0.0.33"

"external-editor@^3.0.3":
  "integrity" "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU="
  "resolved" "https://registry.npm.taobao.org/external-editor/download/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^0.3.1":
  "integrity" "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE="
  "resolved" "https://registry.npm.taobao.org/extglob/download/extglob-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "is-extglob" "^1.0.0"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extract-from-css@^0.4.4":
  "integrity" "sha1-HqffLnx8brmSL6COitrqSG9vj5I="
  "resolved" "https://registry.npm.taobao.org/extract-from-css/download/extract-from-css-0.4.4.tgz"
  "version" "0.4.4"
  dependencies:
    "css" "^2.1.0"

"extract-zip@1.6.7":
  "integrity" "sha1-qEC0uK9kAyZMjbV/Txp0Mz74H+k="
  "resolved" "https://registry.npm.taobao.org/extract-zip/download/extract-zip-1.6.7.tgz"
  "version" "1.6.7"
  dependencies:
    "concat-stream" "1.6.2"
    "debug" "2.6.9"
    "mkdirp" "0.5.1"
    "yauzl" "2.4.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^1.0.0":
  "integrity" "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="
  "resolved" "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz"
  "version" "1.1.0"

"fast-deep-equal@^2.0.1":
  "integrity" "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk="
  "resolved" "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz"
  "version" "2.0.1"

"fast-glob@^2.2.6":
  "integrity" "sha1-aVOFfDr6R1//ku5gFdUtpwpM050="
  "resolved" "https://registry.npm.taobao.org/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1575197566634&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz"
  "version" "2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    "glob-parent" "^3.1.0"
    "is-glob" "^4.0.0"
    "merge2" "^1.2.3"
    "micromatch" "^3.1.10"

"fast-json-stable-stringify@^2.0.0", "fast-json-stable-stringify@2.x":
  "integrity" "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="
  "resolved" "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz"
  "version" "2.0.0"

"fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastparse@^1.1.1":
  "integrity" "sha1-kXKMWllC7O2FMSg8eUQe5BIsNak="
  "resolved" "https://registry.npm.taobao.org/fastparse/download/fastparse-1.1.2.tgz"
  "version" "1.1.2"

"faye-websocket@^0.10.0":
  "integrity" "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ="
  "resolved" "https://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.10.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffaye-websocket%2Fdownload%2Ffaye-websocket-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "websocket-driver" ">=0.5.1"

"faye-websocket@~0.11.1":
  "integrity" "sha1-XA6aiWjokSwoZjn96XeosgnyUI4="
  "resolved" "https://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.11.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffaye-websocket%2Fdownload%2Ffaye-websocket-0.11.3.tgz"
  "version" "0.11.3"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fb-watchman@^2.0.0":
  "integrity" "sha1-VOmr99+i8mzZsWNsWIwa/AXeXVg="
  "resolved" "https://registry.npm.taobao.org/fb-watchman/download/fb-watchman-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "bser" "^2.0.0"

"fd-slicer@~1.0.1":
  "integrity" "sha1-i1vL2ewyfFBBv5qwI/1nUPEXfmU="
  "resolved" "https://registry.npm.taobao.org/fd-slicer/download/fd-slicer-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "pend" "~1.2.0"

"fd-slicer@~1.1.0":
  "integrity" "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4="
  "resolved" "https://registry.npm.taobao.org/fd-slicer/download/fd-slicer-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "pend" "~1.2.0"

"fibers@^4.0.2":
  "integrity" "sha512-FhICi1K4WZh9D6NC18fh2ODF3EWy1z0gzIdV9P7+s2pRjfRBnCkMDJ6x3bV1DkVymKH8HGrQa/FNOBjYvnJ/tQ=="
  "resolved" "https://registry.npmjs.org/fibers/-/fibers-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "detect-libc" "^1.0.3"

"figgy-pudding@^3.5.1":
  "integrity" "sha1-hiRwESkBxyeg5JWoB0S9W6odZ5A="
  "resolved" "https://registry.npm.taobao.org/figgy-pudding/download/figgy-pudding-3.5.1.tgz"
  "version" "3.5.1"

"figures@^1.7.0":
  "integrity" "sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4="
  "resolved" "https://registry.npm.taobao.org/figures/download/figures-1.7.0.tgz?cache=0&sync_timestamp=1571715201547&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffigures%2Fdownload%2Ffigures-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"
    "object-assign" "^4.1.0"

"figures@^2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "https://registry.npm.taobao.org/figures/download/figures-2.0.0.tgz?cache=0&sync_timestamp=1571715201547&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffigures%2Fdownload%2Ffigures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"figures@^3.0.0":
  "integrity" "sha1-SxmN0H2NcVMGQoZK8tRd2eRZxOw="
  "resolved" "https://registry.npm.taobao.org/figures/download/figures-3.1.0.tgz?cache=0&sync_timestamp=1571715201547&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffigures%2Fdownload%2Ffigures-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^2.0.0":
  "integrity" "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E="
  "resolved" "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "flat-cache" "^1.2.1"
    "object-assign" "^4.0.1"

"file-entry-cache@^5.0.1":
  "integrity" "sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w="
  "resolved" "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "flat-cache" "^2.0.1"

"file-loader@^3.0.1":
  "integrity" "sha1-+OC6C1mZGLUa3+RdZtHnca1WD6o="
  "resolved" "https://registry.npm.taobao.org/file-loader/download/file-loader-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "loader-utils" "^1.0.2"
    "schema-utils" "^1.0.0"

"filename-regex@^2.0.0":
  "integrity" "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY="
  "resolved" "https://registry.npm.taobao.org/filename-regex/download/filename-regex-2.0.1.tgz"
  "version" "2.0.1"

"fileset@^2.0.2":
  "integrity" "sha1-jnVIqW08wjJ+5eZ0FocjozO7oqA="
  "resolved" "https://registry.npm.taobao.org/fileset/download/fileset-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "glob" "^7.0.3"
    "minimatch" "^3.0.3"

"filesize@^3.6.1":
  "integrity" "sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc="
  "resolved" "https://registry.npm.taobao.org/filesize/download/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^2.1.0":
  "integrity" "sha1-6x53OrsFbc2N8r/favWbizqTZWU="
  "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "is-number" "^2.1.0"
    "isobject" "^2.0.0"
    "randomatic" "^3.0.0"
    "repeat-element" "^1.1.2"
    "repeat-string" "^1.5.2"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"finalhandler@~1.1.2":
  "integrity" "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0="
  "resolved" "https://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-babel-config@^1.1.0":
  "integrity" "sha1-qbezF+tbmGDNqdVHQKjIM3oig6I="
  "resolved" "https://registry.npm.taobao.org/find-babel-config/download/find-babel-config-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "json5" "^0.5.1"
    "path-exists" "^3.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^1.0.0":
  "integrity" "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8="
  "resolved" "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^1.0.0"
    "pkg-dir" "^2.0.0"

"find-cache-dir@^2.0.0", "find-cache-dir@^2.1.0":
  "integrity" "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="
  "resolved" "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^2.0.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.1.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^1.2.1":
  "integrity" "sha1-LC73dSXMKSkAff/6HdMUqpyd7m8="
  "resolved" "https://registry.npm.taobao.org/flat-cache/download/flat-cache-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "circular-json" "^0.3.1"
    "graceful-fs" "^4.1.2"
    "rimraf" "~2.6.2"
    "write" "^0.2.1"

"flat-cache@^2.0.1":
  "integrity" "sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA="
  "resolved" "https://registry.npm.taobao.org/flat-cache/download/flat-cache-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "flatted" "^2.0.0"
    "rimraf" "2.6.3"
    "write" "1.0.3"

"flatted@^2.0.0":
  "integrity" "sha1-aeV8qo8OrLwoHS4stFjUb9tEngg="
  "resolved" "https://registry.npm.taobao.org/flatted/download/flatted-2.0.1.tgz?cache=0&sync_timestamp=1561466276595&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fflatted%2Fdownload%2Fflatted-2.0.1.tgz"
  "version" "2.0.1"

"flush-write-stream@^1.0.0":
  "integrity" "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug="
  "resolved" "https://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0", "follow-redirects@1.5.10":
  "integrity" "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio="
  "resolved" "https://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "debug" "=3.1.0"

"for-in@^1.0.1", "for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"for-own@^0.1.4":
  "integrity" "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4="
  "resolved" "https://registry.npm.taobao.org/for-own/download/for-own-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "for-in" "^1.0.1"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"fork-ts-checker-webpack-plugin@^0.5.2":
  "integrity" "sha1-pzs2ML0KaUCabkgk5UwDpi/oLY8="
  "resolved" "https://registry.npm.taobao.org/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-0.5.2.tgz?cache=0&sync_timestamp=1574695389755&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffork-ts-checker-webpack-plugin%2Fdownload%2Ffork-ts-checker-webpack-plugin-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "babel-code-frame" "^6.22.0"
    "chalk" "^2.4.1"
    "chokidar" "^2.0.4"
    "micromatch" "^3.1.10"
    "minimatch" "^3.0.4"
    "tapable" "^1.0.0"

"form-data@~2.3.2":
  "integrity" "sha1-3M5SwF9kTymManq5Nr1yTO/786Y="
  "resolved" "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz?cache=0&sync_timestamp=1573027037564&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@~0.1.2":
  "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
  "resolved" "https://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz"
  "version" "0.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-extra@^4.0.2":
  "integrity" "sha1-DYUhIuW8W+tFP7Ao6cDJvzY0DJQ="
  "resolved" "https://registry.npm.taobao.org/fs-extra/download/fs-extra-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-extra@^7.0.1":
  "integrity" "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk="
  "resolved" "https://registry.npm.taobao.org/fs-extra/download/fs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-extra@5.0.0":
  "integrity" "sha1-QU0BEM3QZwVzTQVWUsVBEmDDGr0="
  "resolved" "https://registry.npm.taobao.org/fs-extra/download/fs-extra-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-plus@^3.0.2":
  "integrity" "sha1-AsCFugoBMITP8vPomxfGDB2bSrU="
  "resolved" "https://registry.npm.taobao.org/fs-plus/download/fs-plus-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "async" "^1.5.2"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.2"
    "underscore-plus" "1.x"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.0.2", "function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"get-caller-file@^1.0.1":
  "integrity" "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o="
  "resolved" "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.3.tgz"
  "version" "1.0.3"

"get-caller-file@^2.0.1":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-own-enumerable-property-symbols@^3.0.0":
  "integrity" "sha1-b3dk+I6hHgtRS9m9hgoTIlmZLKQ="
  "resolved" "https://registry.npm.taobao.org/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.1.tgz?cache=0&sync_timestamp=1570169964804&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-own-enumerable-property-symbols%2Fdownload%2Fget-own-enumerable-property-symbols-3.0.1.tgz"
  "version" "3.0.1"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0":
  "integrity" "sha1-ASA83JJZf5uQkGfD5lbMH008Tck="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getos@3.1.1":
  "integrity" "sha1-lnqBPM6v7gFWsEg/fP+ls+/wKcU="
  "resolved" "https://registry.npm.taobao.org/getos/download/getos-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "async" "2.6.1"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-base@^0.3.0":
  "integrity" "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q="
  "resolved" "https://registry.npm.taobao.org/glob-base/download/glob-base-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "glob-parent" "^2.0.0"
    "is-glob" "^2.0.0"

"glob-parent@^2.0.0":
  "integrity" "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg="
  "resolved" "https://registry.npm.taobao.org/glob-parent/download/glob-parent-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-glob" "^2.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@^5.0.0":
  "integrity" "sha1-X0wdHnSNMM1zrSlEs1d6gbCB6MI="
  "resolved" "https://registry.npm.taobao.org/glob-parent/download/glob-parent-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.3.0":
  "integrity" "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="
  "resolved" "https://registry.npm.taobao.org/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  "version" "0.3.0"

"glob@^7.0.3", "glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4", "glob@^7.1.6":
  "integrity" "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY="
  "resolved" "https://registry.npm.taobao.org/glob/download/glob-7.1.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz"
  "version" "7.1.6"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global-dirs@^0.1.0":
  "integrity" "sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU="
  "resolved" "https://registry.npm.taobao.org/global-dirs/download/global-dirs-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "ini" "^1.3.4"

"globals@^11.0.1", "globals@^11.1.0", "globals@^11.12.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "https://registry.npm.taobao.org/globals/download/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^12.1.0":
  "integrity" "sha1-HlZO5cTd7SqwmLD4jyRwKjxWvhM="
  "resolved" "https://registry.npm.taobao.org/globals/download/globals-12.3.0.tgz"
  "version" "12.3.0"
  dependencies:
    "type-fest" "^0.8.1"

"globals@^9.18.0":
  "integrity" "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo="
  "resolved" "https://registry.npm.taobao.org/globals/download/globals-9.18.0.tgz"
  "version" "9.18.0"

"globby@^6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://registry.npm.taobao.org/globby/download/globby-6.1.0.tgz?cache=0&sync_timestamp=1562307970751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://registry.npm.taobao.org/globby/download/globby-7.1.1.tgz?cache=0&sync_timestamp=1562307970751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"globby@^9.2.0":
  "integrity" "sha1-/QKacGxwPSm90XD0tts6P3p8tj0="
  "resolved" "https://registry.npm.taobao.org/globby/download/globby-9.2.0.tgz?cache=0&sync_timestamp=1562307970751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-9.2.0.tgz"
  "version" "9.2.0"
  dependencies:
    "@types/glob" "^7.1.1"
    "array-union" "^1.0.2"
    "dir-glob" "^2.2.2"
    "fast-glob" "^2.2.6"
    "glob" "^7.1.3"
    "ignore" "^4.0.3"
    "pify" "^4.0.1"
    "slash" "^2.0.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6":
  "integrity" "sha1-ShL/G2A3bvCYYsIJPt2Qgyi+hCM="
  "resolved" "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.2.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.3.tgz"
  "version" "4.2.3"

"growly@^1.3.0":
  "integrity" "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE="
  "resolved" "https://registry.npm.taobao.org/growly/download/growly-1.3.0.tgz"
  "version" "1.3.0"

"gzip-size@^5.0.0":
  "integrity" "sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ="
  "resolved" "https://registry.npm.taobao.org/gzip-size/download/gzip-size-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^4.0.1"

"handle-thing@^2.0.0":
  "integrity" "sha1-DgOWlf9QyT/CiFV9aW88HcZ3Z1Q="
  "resolved" "https://registry.npm.taobao.org/handle-thing/download/handle-thing-2.0.0.tgz"
  "version" "2.0.0"

"handlebars@^4.0.3", "handlebars@^4.1.2":
  "integrity" "sha1-XPdb2HFPdgVxNRGla+fDSb7LBII="
  "resolved" "https://registry.npm.taobao.org/handlebars/download/handlebars-4.5.3.tgz"
  "version" "4.5.3"
  dependencies:
    "neo-async" "^2.6.0"
    "optimist" "^0.6.1"
    "source-map" "^0.6.1"
  optionalDependencies:
    "uglify-js" "^3.1.4"

"har-schema@^2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.0":
  "integrity" "sha1-HvievT5JllV2de7ZiTEQ3DUPoIA="
  "resolved" "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "ajv" "^6.5.5"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-flag@^1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "https://registry.npm.taobao.org/has-flag/download/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="
  "resolved" "https://registry.npm.taobao.org/has-flag/download/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.0", "has-symbols@^1.0.1":
  "integrity" "sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg="
  "resolved" "https://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.1.tgz?cache=0&sync_timestamp=1573950770764&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhas-symbols%2Fdownload%2Fhas-symbols-1.0.1.tgz"
  "version" "1.0.1"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.1", "has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "https://registry.npm.taobao.org/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg="
  "resolved" "https://registry.npm.taobao.org/hash-base/download/hash-base-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhash-sum%2Fdownload%2Fhash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I="
  "resolved" "https://registry.npm.taobao.org/hash.js/download/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.1.0", "he@1.2.x":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "https://registry.npm.taobao.org/he/download/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4="
  "resolved" "https://registry.npm.taobao.org/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"highlight.js@^9.6.0":
  "integrity" "sha1-aDaNA5/+HGIRvMB+SD2vld4+QD4="
  "resolved" "https://registry.npm.taobao.org/highlight.js/download/highlight.js-9.16.2.tgz"
  "version" "9.16.2"

"hmac-drbg@^1.0.0":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoek@4.x.x":
  "integrity" "sha1-ljRQKqEsRF3Vp8VzS1cruHOKrLs="
  "resolved" "https://registry.npm.taobao.org/hoek/download/hoek-4.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhoek%2Fdownload%2Fhoek-4.2.1.tgz"
  "version" "4.2.1"

"home-or-tmp@^2.0.0":
  "integrity" "sha1-42w/LSyufXRqhX440Y1fMqeILbg="
  "resolved" "https://registry.npm.taobao.org/home-or-tmp/download/home-or-tmp-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.1"

"hoopy@^0.1.4":
  "integrity" "sha1-YJIH1mEQADOpqUAq096mdzgcGx0="
  "resolved" "https://registry.npm.taobao.org/hoopy/download/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-dZz88sTRVq3lmwst+r3cQqa5xww="
  "resolved" "https://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.8.5.tgz"
  "version" "2.8.5"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://registry.npm.taobao.org/hsl-regex/download/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-comment-regex@^1.1.0":
  "integrity" "sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c="
  "resolved" "https://registry.npm.taobao.org/html-comment-regex/download/html-comment-regex-1.1.2.tgz"
  "version" "1.1.2"

"html-encoding-sniffer@^1.0.2":
  "integrity" "sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg="
  "resolved" "https://registry.npm.taobao.org/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "whatwg-encoding" "^1.0.1"

"html-entities@^1.2.1":
  "integrity" "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8="
  "resolved" "https://registry.npm.taobao.org/html-entities/download/html-entities-1.2.1.tgz"
  "version" "1.2.1"

"html-minifier@^3.2.3":
  "integrity" "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="
  "resolved" "https://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-tags@^2.0.0":
  "integrity" "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos="
  "resolved" "https://registry.npm.taobao.org/html-tags/download/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-webpack-plugin@^3.2.0", "html-webpack-plugin@>=2.26.0":
  "integrity" "sha1-sBq71yOsqqeze2r0SS69oD2d03s="
  "resolved" "https://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-webpack-plugin%2Fdownload%2Fhtml-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"htmlparser2@^3.3.0":
  "integrity" "sha1-vWedw/WYl7ajS7EHSchVu1OpOS8="
  "resolved" "https://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "^1.3.1"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.1.1"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npm.taobao.org/http-deceiver/download/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@~1.7.2", "http-errors@1.7.2":
  "integrity" "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8="
  "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.7.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-parser-js@>=0.4.0 <0.4.11":
  "integrity" "sha1-ksnBN0w1CF912zWexWzCV8u5P6Q="
  "resolved" "https://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.4.10.tgz"
  "version" "0.4.10"

"http-proxy-middleware@0.19.1":
  "integrity" "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo="
  "resolved" "https://registry.npm.taobao.org/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz?cache=0&sync_timestamp=1572571366599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.17.0":
  "integrity" "sha1-2+VfY+daNH2389mZdPJpKjFKajo="
  "resolved" "https://registry.npm.taobao.org/http-proxy/download/http-proxy-1.18.0.tgz?cache=0&sync_timestamp=1572571366273&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-proxy%2Fdownload%2Fhttp-proxy-1.18.0.tgz"
  "version" "1.18.0"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://registry.npm.taobao.org/https-browserify/download/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"human-signals@^1.1.1":
  "integrity" "sha1-xbHNFPUK6uCatsWf5jujOV/k36M="
  "resolved" "https://registry.npm.taobao.org/human-signals/download/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"iconv-lite@^0.4.17", "iconv-lite@^0.4.24", "iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-replace-symbols@^1.1.0":
  "integrity" "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="
  "resolved" "https://registry.npm.taobao.org/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz"
  "version" "1.1.0"

"icss-utils@^2.1.0":
  "integrity" "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI="
  "resolved" "https://registry.npm.taobao.org/icss-utils/download/icss-utils-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficss-utils%2Fdownload%2Ficss-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^6.0.1"

"ieee754@^1.1.4":
  "integrity" "sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q="
  "resolved" "https://registry.npm.taobao.org/ieee754/download/ieee754-1.1.13.tgz"
  "version" "1.1.13"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.3", "ignore@^3.3.5":
  "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
  "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@^4.0.3":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^4.0.6":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.0.2":
  "integrity" "sha1-hLez2+ZFUrbvDsqZ9nQ9vsbZet8="
  "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-5.1.4.tgz"
  "version" "5.1.4"

"import-cwd@^2.0.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "https://registry.npm.taobao.org/import-cwd/download/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-2.0.0.tgz?cache=0&sync_timestamp=1573665120798&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0":
  "integrity" "sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.2.1.tgz?cache=0&sync_timestamp=1573665120798&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-from@^2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "https://registry.npm.taobao.org/import-from/download/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^1.0.0":
  "integrity" "sha1-Xk/9wD9P5sAJxnKb6yljHC+CJ7w="
  "resolved" "https://registry.npm.taobao.org/import-local/download/import-local-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pkg-dir" "^2.0.0"
    "resolve-cwd" "^2.0.0"

"import-local@^2.0.0":
  "integrity" "sha1-VQcL44pZk88Y72236WH1vuXFoJ0="
  "resolved" "https://registry.npm.taobao.org/import-local/download/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "^3.0.0"
    "resolve-cwd" "^2.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^2.1.0":
  "integrity" "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA="
  "resolved" "https://registry.npm.taobao.org/indent-string/download/indent-string-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "repeating" "^2.0.0"

"indent-string@^3.0.0":
  "integrity" "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok="
  "resolved" "https://registry.npm.taobao.org/indent-string/download/indent-string-3.2.0.tgz"
  "version" "3.2.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"infer-owner@^1.0.3":
  "integrity" "sha1-xM78qo5RBRwqQLos6KPScpWvlGc="
  "resolved" "https://registry.npm.taobao.org/infer-owner/download/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz"
  "version" "2.0.3"

"ini@^1.3.4":
  "integrity" "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc="
  "resolved" "https://registry.npm.taobao.org/ini/download/ini-1.3.5.tgz"
  "version" "1.3.5"

"inquirer@^3.0.6":
  "integrity" "sha1-ndLyrXZdyrH/BEO0kUQqILoifck="
  "resolved" "https://registry.npm.taobao.org/inquirer/download/inquirer-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.0"
    "cli-cursor" "^2.1.0"
    "cli-width" "^2.0.0"
    "external-editor" "^2.0.4"
    "figures" "^2.0.0"
    "lodash" "^4.3.0"
    "mute-stream" "0.0.7"
    "run-async" "^2.2.0"
    "rx-lite" "^4.0.8"
    "rx-lite-aggregates" "^4.0.8"
    "string-width" "^2.1.0"
    "strip-ansi" "^4.0.0"
    "through" "^2.3.6"

"inquirer@^7.0.0":
  "integrity" "sha1-nisDLd532h2124BHWLj+o6lwUZo="
  "resolved" "https://registry.npm.taobao.org/inquirer/download/inquirer-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^2.4.2"
    "cli-cursor" "^3.1.0"
    "cli-width" "^2.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.15"
    "mute-stream" "0.0.8"
    "run-async" "^2.2.0"
    "rxjs" "^6.4.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^5.1.0"
    "through" "^2.3.6"

"internal-ip@^4.3.0":
  "integrity" "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc="
  "resolved" "https://registry.npm.taobao.org/internal-ip/download/internal-ip-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "default-gateway" "^4.2.0"
    "ipaddr.js" "^1.9.0"

"invariant@^2.2.2", "invariant@^2.2.4":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "https://registry.npm.taobao.org/invariant/download/invariant-2.2.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finvariant%2Fdownload%2Finvariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"invert-kv@^2.0.0":
  "integrity" "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI="
  "resolved" "https://registry.npm.taobao.org/invert-kv/download/invert-kv-2.0.0.tgz"
  "version" "2.0.0"

"ip-regex@^2.1.0":
  "integrity" "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="
  "resolved" "https://registry.npm.taobao.org/ip-regex/download/ip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npm.taobao.org/ip/download/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@^1.9.0", "ipaddr.js@1.9.0":
  "integrity" "sha1-N9905DCg5HVQ/lSi3v4w2KzZX2U="
  "resolved" "https://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.9.0.tgz"
  "version" "1.9.0"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-absolute-url@^3.0.3":
  "integrity" "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg="
  "resolved" "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha1-P6+WbHy6D/Q3+zH2JQCC/PBEjPM="
  "resolved" "https://registry.npm.taobao.org/is-arguments/download/is-arguments-1.0.4.tgz"
  "version" "1.0.4"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
  "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "https://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz?cache=0&sync_timestamp=1569905349018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-buffer@^2.0.2":
  "integrity" "sha1-PlcvI8hBGlz9lVfISeNmXgspBiM="
  "resolved" "https://registry.npm.taobao.org/is-buffer/download/is-buffer-2.0.4.tgz?cache=0&sync_timestamp=1569905349018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-2.0.4.tgz"
  "version" "2.0.4"

"is-buffer@~1.1.6":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4":
  "integrity" "sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU="
  "resolved" "https://registry.npm.taobao.org/is-callable/download/is-callable-1.1.4.tgz"
  "version" "1.1.4"

"is-ci@^1.0.10", "is-ci@1.2.1":
  "integrity" "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw="
  "resolved" "https://registry.npm.taobao.org/is-ci/download/is-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-ci@^2.0.0":
  "integrity" "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw="
  "resolved" "https://registry.npm.taobao.org/is-ci/download/is-ci-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ci-info" "^2.0.0"

"is-color-stop@^1.0.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://registry.npm.taobao.org/is-color-stop/download/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY="
  "resolved" "https://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.1.tgz"
  "version" "1.0.1"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-dotfile@^1.0.0":
  "integrity" "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE="
  "resolved" "https://registry.npm.taobao.org/is-dotfile/download/is-dotfile-1.0.3.tgz"
  "version" "1.0.3"

"is-equal-shallow@^0.1.3":
  "integrity" "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ="
  "resolved" "https://registry.npm.taobao.org/is-equal-shallow/download/is-equal-shallow-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "is-primitive" "^2.0.0"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^1.0.0":
  "integrity" "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="
  "resolved" "https://registry.npm.taobao.org/is-extglob/download/is-extglob-1.0.0.tgz"
  "version" "1.0.0"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finite@^1.0.0":
  "integrity" "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko="
  "resolved" "https://registry.npm.taobao.org/is-finite/download/is-finite-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-generator-fn@^1.0.0":
  "integrity" "sha1-lp1J4bszKfa7fwkIm+JleLLd1Go="
  "resolved" "https://registry.npm.taobao.org/is-generator-fn/download/is-generator-fn-1.0.0.tgz"
  "version" "1.0.0"

"is-generator-fn@^2.0.0":
  "integrity" "sha1-fRQK3DiarzARqPKipM+m+q3/sRg="
  "resolved" "https://registry.npm.taobao.org/is-generator-fn/download/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-glob@^2.0.0":
  "integrity" "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM="
  "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extglob" "^1.0.0"

"is-glob@^2.0.1":
  "integrity" "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM="
  "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extglob" "^1.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1":
  "integrity" "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw="
  "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-extglob" "^2.1.1"

"is-installed-globally@0.1.0":
  "integrity" "sha1-Df2Y9akRFxbdU13aZJL2e/PSWoA="
  "resolved" "https://registry.npm.taobao.org/is-installed-globally/download/is-installed-globally-0.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-installed-globally%2Fdownload%2Fis-installed-globally-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "global-dirs" "^0.1.0"
    "is-path-inside" "^1.0.0"

"is-number@^2.1.0":
  "integrity" "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^4.0.0":
  "integrity" "sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-4.0.0.tgz"
  "version" "4.0.0"

"is-obj@^1.0.0", "is-obj@^1.0.1":
  "integrity" "sha1-PkcprB9f3gJc19g6iW2rn09n2w8="
  "resolved" "https://registry.npm.taobao.org/is-obj/download/is-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-path-cwd@^2.0.0":
  "integrity" "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s="
  "resolved" "https://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-in-cwd@^2.0.0":
  "integrity" "sha1-v+Lcomxp85cmWkAJljYCk1oFOss="
  "resolved" "https://registry.npm.taobao.org/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-path-inside" "^2.1.0"

"is-path-inside@^1.0.0":
  "integrity" "sha1-jvW33lBDej/cprToZe96pVy0gDY="
  "resolved" "https://registry.npm.taobao.org/is-path-inside/download/is-path-inside-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "path-is-inside" "^1.0.1"

"is-path-inside@^2.1.0":
  "integrity" "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI="
  "resolved" "https://registry.npm.taobao.org/is-path-inside/download/is-path-inside-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "path-is-inside" "^1.0.2"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "https://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-posix-bracket@^0.1.0":
  "integrity" "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q="
  "resolved" "https://registry.npm.taobao.org/is-posix-bracket/download/is-posix-bracket-0.1.1.tgz"
  "version" "0.1.1"

"is-primitive@^2.0.0":
  "integrity" "sha1-IHurkWOEmcB7Kt8kCkGochADRXU="
  "resolved" "https://registry.npm.taobao.org/is-primitive/download/is-primitive-2.0.0.tgz"
  "version" "2.0.0"

"is-promise@^2.1.0":
  "integrity" "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o="
  "resolved" "https://registry.npm.taobao.org/is-promise/download/is-promise-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-promise%2Fdownload%2Fis-promise-2.1.0.tgz"
  "version" "2.1.0"

"is-regex@^1.0.4":
  "integrity" "sha1-VRdIm1RwkbCTDglWVM7SXul+lJE="
  "resolved" "https://registry.npm.taobao.org/is-regex/download/is-regex-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has" "^1.0.1"

"is-regexp@^1.0.0":
  "integrity" "sha1-/S2INUXEa6xaYz57mgnof6LLUGk="
  "resolved" "https://registry.npm.taobao.org/is-regexp/download/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-resolvable@^1.0.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "https://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha1-venDJoDW+uBBKdasnZIc54FfeOM="
  "resolved" "https://registry.npm.taobao.org/is-stream/download/is-stream-2.0.0.tgz"
  "version" "2.0.0"

"is-svg@^3.0.0":
  "integrity" "sha1-kyHb0pwhLlypnE+peUxxS8r6L3U="
  "resolved" "https://registry.npm.taobao.org/is-svg/download/is-svg-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "html-comment-regex" "^1.1.0"

"is-symbol@^1.0.2":
  "integrity" "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc="
  "resolved" "https://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.3.tgz?cache=0&sync_timestamp=1574296387814&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-symbol%2Fdownload%2Fis-symbol-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "has-symbols" "^1.0.1"

"is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-utf8@^0.2.0":
  "integrity" "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="
  "resolved" "https://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-whitespace@^0.3.0":
  "integrity" "sha1-Fjnssb4DauxppUy7QBz77XEUq38="
  "resolved" "https://registry.npm.taobao.org/is-whitespace/download/is-whitespace-0.3.0.tgz"
  "version" "0.3.0"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "https://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npm.taobao.org/is-wsl/download/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz?cache=0&sync_timestamp=1562592096220&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fisarray%2Fdownload%2Fisarray-1.0.0.tgz"
  "version" "1.0.0"

"isemail@3.x.x":
  "integrity" "sha1-WTEKAhkxqfsGu7UeFVzgs/I2gyw="
  "resolved" "https://registry.npm.taobao.org/isemail/download/isemail-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "punycode" "2.x.x"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz"
  "version" "0.1.2"

"istanbul-api@^1.3.1":
  "integrity" "sha1-qGx3DSsD4R4/d4zXrt2C0nIgkqo="
  "resolved" "https://registry.npm.taobao.org/istanbul-api/download/istanbul-api-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "async" "^2.1.4"
    "fileset" "^2.0.2"
    "istanbul-lib-coverage" "^1.2.1"
    "istanbul-lib-hook" "^1.2.2"
    "istanbul-lib-instrument" "^1.10.2"
    "istanbul-lib-report" "^1.1.5"
    "istanbul-lib-source-maps" "^1.2.6"
    "istanbul-reports" "^1.5.1"
    "js-yaml" "^3.7.0"
    "mkdirp" "^0.5.1"
    "once" "^1.4.0"

"istanbul-lib-coverage@^1.2.0", "istanbul-lib-coverage@^1.2.1":
  "integrity" "sha1-zPftzQoLubj3Kf7rCTBHD5r2ZPA="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-coverage/download/istanbul-lib-coverage-1.2.1.tgz"
  "version" "1.2.1"

"istanbul-lib-coverage@^2.0.2", "istanbul-lib-coverage@^2.0.5":
  "integrity" "sha1-Z18KtpUD+tSx2En3NrqsqAM0T0k="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-coverage/download/istanbul-lib-coverage-2.0.5.tgz"
  "version" "2.0.5"

"istanbul-lib-hook@^1.2.2":
  "integrity" "sha1-vGvwfxKmQfvxyFOR0Nqo8K6mv4Y="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-hook/download/istanbul-lib-hook-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "append-transform" "^0.4.0"

"istanbul-lib-instrument@^1.10.1", "istanbul-lib-instrument@^1.10.2":
  "integrity" "sha1-H1XtEKw8R/K93dUweTUSZ1TQqco="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-instrument/download/istanbul-lib-instrument-1.10.2.tgz?cache=0&sync_timestamp=1572639536955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-lib-instrument%2Fdownload%2Fistanbul-lib-instrument-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "babel-generator" "^6.18.0"
    "babel-template" "^6.16.0"
    "babel-traverse" "^6.18.0"
    "babel-types" "^6.18.0"
    "babylon" "^6.18.0"
    "istanbul-lib-coverage" "^1.2.1"
    "semver" "^5.3.0"

"istanbul-lib-instrument@^3.0.1", "istanbul-lib-instrument@^3.3.0":
  "integrity" "sha1-pfY9kfC7wMPkee9MXeAnM17G1jA="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-instrument/download/istanbul-lib-instrument-3.3.0.tgz?cache=0&sync_timestamp=1572639536955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-lib-instrument%2Fdownload%2Fistanbul-lib-instrument-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@babel/generator" "^7.4.0"
    "@babel/parser" "^7.4.3"
    "@babel/template" "^7.4.0"
    "@babel/traverse" "^7.4.3"
    "@babel/types" "^7.4.0"
    "istanbul-lib-coverage" "^2.0.5"
    "semver" "^6.0.0"

"istanbul-lib-report@^1.1.5":
  "integrity" "sha1-8qZX/GKC+WFwqvKB6zCkWPf0Fww="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-report/download/istanbul-lib-report-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "istanbul-lib-coverage" "^1.2.1"
    "mkdirp" "^0.5.1"
    "path-parse" "^1.0.5"
    "supports-color" "^3.1.2"

"istanbul-lib-report@^2.0.4":
  "integrity" "sha1-WoETzXRtQ8SInro2qxDn1QybTzM="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-report/download/istanbul-lib-report-2.0.8.tgz"
  "version" "2.0.8"
  dependencies:
    "istanbul-lib-coverage" "^2.0.5"
    "make-dir" "^2.1.0"
    "supports-color" "^6.1.0"

"istanbul-lib-source-maps@^1.2.4", "istanbul-lib-source-maps@^1.2.6":
  "integrity" "sha1-N7n/ZhWA+PyhEjJ1LuQuCMZnXY8="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-source-maps/download/istanbul-lib-source-maps-1.2.6.tgz?cache=0&sync_timestamp=1573916185030&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-lib-source-maps%2Fdownload%2Fistanbul-lib-source-maps-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "debug" "^3.1.0"
    "istanbul-lib-coverage" "^1.2.1"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.6.1"
    "source-map" "^0.5.3"

"istanbul-lib-source-maps@^3.0.1":
  "integrity" "sha1-KEmXxIIRdS7EhiU9qX44ed77qMg="
  "resolved" "https://registry.npm.taobao.org/istanbul-lib-source-maps/download/istanbul-lib-source-maps-3.0.6.tgz?cache=0&sync_timestamp=1573916185030&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-lib-source-maps%2Fdownload%2Fistanbul-lib-source-maps-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^2.0.5"
    "make-dir" "^2.1.0"
    "rimraf" "^2.6.3"
    "source-map" "^0.6.1"

"istanbul-reports@^1.5.1":
  "integrity" "sha1-l+Tb87UV6MSEyuoV1lJO69P/Tho="
  "resolved" "https://registry.npm.taobao.org/istanbul-reports/download/istanbul-reports-1.5.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-reports%2Fdownload%2Fistanbul-reports-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "handlebars" "^4.0.3"

"istanbul-reports@^2.2.6":
  "integrity" "sha1-e08mYNgrKTA6j+YJH4ykvwWNoa8="
  "resolved" "https://registry.npm.taobao.org/istanbul-reports/download/istanbul-reports-2.2.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-reports%2Fdownload%2Fistanbul-reports-2.2.6.tgz"
  "version" "2.2.6"
  dependencies:
    "handlebars" "^4.1.2"

"javascript-stringify@^1.6.0":
  "integrity" "sha1-FC0RHzpuPa6PSpr9d9RYVbWpzOM="
  "resolved" "https://registry.npm.taobao.org/javascript-stringify/download/javascript-stringify-1.6.0.tgz?cache=0&sync_timestamp=1572948916758&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjavascript-stringify%2Fdownload%2Fjavascript-stringify-1.6.0.tgz"
  "version" "1.6.0"

"jest-changed-files@^23.4.2":
  "integrity" "sha1-Hu1og3DNXuuv5K6T00uztklo/oM="
  "resolved" "https://registry.npm.taobao.org/jest-changed-files/download/jest-changed-files-23.4.2.tgz"
  "version" "23.4.2"
  dependencies:
    "throat" "^4.0.0"

"jest-changed-files@^24.9.0":
  "integrity" "sha1-CNjBXreaf6P8mCabwUtFHugvgDk="
  "resolved" "https://registry.npm.taobao.org/jest-changed-files/download/jest-changed-files-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "execa" "^1.0.0"
    "throat" "^4.0.0"

"jest-cli@^23.6.0":
  "integrity" "sha1-YauRd0Qzj0Q+8rqigt3/3WWKXaQ="
  "resolved" "https://registry.npm.taobao.org/jest-cli/download/jest-cli-23.6.0.tgz?cache=0&sync_timestamp=1566444314158&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-cli%2Fdownload%2Fjest-cli-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.1"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "graceful-fs" "^4.1.11"
    "import-local" "^1.0.0"
    "is-ci" "^1.0.10"
    "istanbul-api" "^1.3.1"
    "istanbul-lib-coverage" "^1.2.0"
    "istanbul-lib-instrument" "^1.10.1"
    "istanbul-lib-source-maps" "^1.2.4"
    "jest-changed-files" "^23.4.2"
    "jest-config" "^23.6.0"
    "jest-environment-jsdom" "^23.4.0"
    "jest-get-type" "^22.1.0"
    "jest-haste-map" "^23.6.0"
    "jest-message-util" "^23.4.0"
    "jest-regex-util" "^23.3.0"
    "jest-resolve-dependencies" "^23.6.0"
    "jest-runner" "^23.6.0"
    "jest-runtime" "^23.6.0"
    "jest-snapshot" "^23.6.0"
    "jest-util" "^23.4.0"
    "jest-validate" "^23.6.0"
    "jest-watcher" "^23.4.0"
    "jest-worker" "^23.2.0"
    "micromatch" "^2.3.11"
    "node-notifier" "^5.2.1"
    "prompts" "^0.1.9"
    "realpath-native" "^1.0.0"
    "rimraf" "^2.5.4"
    "slash" "^1.0.0"
    "string-length" "^2.0.0"
    "strip-ansi" "^4.0.0"
    "which" "^1.2.12"
    "yargs" "^11.0.0"

"jest-cli@^24.9.0":
  "integrity" "sha1-rS3mLQdHLUGcarwwH8QyuYsQ0q8="
  "resolved" "https://registry.npm.taobao.org/jest-cli/download/jest-cli-24.9.0.tgz?cache=0&sync_timestamp=1566444314158&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-cli%2Fdownload%2Fjest-cli-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/core" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "chalk" "^2.0.1"
    "exit" "^0.1.2"
    "import-local" "^2.0.0"
    "is-ci" "^2.0.0"
    "jest-config" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-validate" "^24.9.0"
    "prompts" "^2.0.1"
    "realpath-native" "^1.1.0"
    "yargs" "^13.3.0"

"jest-config@^23.6.0":
  "integrity" "sha1-+CVGqQreLYxwJvv2rFIH/CL46x0="
  "resolved" "https://registry.npm.taobao.org/jest-config/download/jest-config-23.6.0.tgz?cache=0&sync_timestamp=1566444304119&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-config%2Fdownload%2Fjest-config-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-core" "^6.0.0"
    "babel-jest" "^23.6.0"
    "chalk" "^2.0.1"
    "glob" "^7.1.1"
    "jest-environment-jsdom" "^23.4.0"
    "jest-environment-node" "^23.4.0"
    "jest-get-type" "^22.1.0"
    "jest-jasmine2" "^23.6.0"
    "jest-regex-util" "^23.3.0"
    "jest-resolve" "^23.6.0"
    "jest-util" "^23.4.0"
    "jest-validate" "^23.6.0"
    "micromatch" "^2.3.11"
    "pretty-format" "^23.6.0"

"jest-config@^24.9.0":
  "integrity" "sha1-+xu8YMc6Rq8DWQcZ76SCXm5N0bU="
  "resolved" "https://registry.npm.taobao.org/jest-config/download/jest-config-24.9.0.tgz?cache=0&sync_timestamp=1566444304119&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-config%2Fdownload%2Fjest-config-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^24.9.0"
    "@jest/types" "^24.9.0"
    "babel-jest" "^24.9.0"
    "chalk" "^2.0.1"
    "glob" "^7.1.1"
    "jest-environment-jsdom" "^24.9.0"
    "jest-environment-node" "^24.9.0"
    "jest-get-type" "^24.9.0"
    "jest-jasmine2" "^24.9.0"
    "jest-regex-util" "^24.3.0"
    "jest-resolve" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-validate" "^24.9.0"
    "micromatch" "^3.1.10"
    "pretty-format" "^24.9.0"
    "realpath-native" "^1.1.0"

"jest-diff@^23.6.0":
  "integrity" "sha1-FQDz8W6FC7PXEjNAgIm+CZ9hDH0="
  "resolved" "https://registry.npm.taobao.org/jest-diff/download/jest-diff-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "^2.0.1"
    "diff" "^3.2.0"
    "jest-get-type" "^22.1.0"
    "pretty-format" "^23.6.0"

"jest-diff@^24.3.0", "jest-diff@^24.9.0":
  "integrity" "sha1-kxt9DVd4obr3RSy4FuMl43JAVdo="
  "resolved" "https://registry.npm.taobao.org/jest-diff/download/jest-diff-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "chalk" "^2.0.1"
    "diff-sequences" "^24.9.0"
    "jest-get-type" "^24.9.0"
    "pretty-format" "^24.9.0"

"jest-docblock@^23.2.0":
  "integrity" "sha1-8IXh8YVI2Z/dabICB+b9VdkTg6c="
  "resolved" "https://registry.npm.taobao.org/jest-docblock/download/jest-docblock-23.2.0.tgz"
  "version" "23.2.0"
  dependencies:
    "detect-newline" "^2.1.0"

"jest-docblock@^24.3.0":
  "integrity" "sha1-eXAgGAK6Vg4cQJLMJcvt9a9ajOI="
  "resolved" "https://registry.npm.taobao.org/jest-docblock/download/jest-docblock-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "detect-newline" "^2.1.0"

"jest-each@^23.6.0":
  "integrity" "sha1-ugw6gqgFQ4cBYTnHM6BSQtPXFXU="
  "resolved" "https://registry.npm.taobao.org/jest-each/download/jest-each-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "^2.0.1"
    "pretty-format" "^23.6.0"

"jest-each@^24.9.0":
  "integrity" "sha1-6y2mAuKmEImNvF8fbfO6hrVfiwU="
  "resolved" "https://registry.npm.taobao.org/jest-each/download/jest-each-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "chalk" "^2.0.1"
    "jest-get-type" "^24.9.0"
    "jest-util" "^24.9.0"
    "pretty-format" "^24.9.0"

"jest-environment-jsdom@^23.4.0":
  "integrity" "sha1-BWp5UrP+pROsYqFAosNox52eYCM="
  "resolved" "https://registry.npm.taobao.org/jest-environment-jsdom/download/jest-environment-jsdom-23.4.0.tgz?cache=0&sync_timestamp=1566444295144&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-environment-jsdom%2Fdownload%2Fjest-environment-jsdom-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "jest-mock" "^23.2.0"
    "jest-util" "^23.4.0"
    "jsdom" "^11.5.1"

"jest-environment-jsdom@^24.9.0":
  "integrity" "sha1-SwgGx/yU+V7bNpppzCd47sK3N1s="
  "resolved" "https://registry.npm.taobao.org/jest-environment-jsdom/download/jest-environment-jsdom-24.9.0.tgz?cache=0&sync_timestamp=1566444295144&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-environment-jsdom%2Fdownload%2Fjest-environment-jsdom-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/environment" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/types" "^24.9.0"
    "jest-mock" "^24.9.0"
    "jest-util" "^24.9.0"
    "jsdom" "^11.5.1"

"jest-environment-node@^23.4.0":
  "integrity" "sha1-V+gO0IQd6jAxZ8zozXlSHeuv3hA="
  "resolved" "https://registry.npm.taobao.org/jest-environment-node/download/jest-environment-node-23.4.0.tgz?cache=0&sync_timestamp=1566444294344&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "jest-mock" "^23.2.0"
    "jest-util" "^23.4.0"

"jest-environment-node@^24.9.0":
  "integrity" "sha1-Mz0tJ5b5aH8q7r8HQrUZ8zwcv9M="
  "resolved" "https://registry.npm.taobao.org/jest-environment-node/download/jest-environment-node-24.9.0.tgz?cache=0&sync_timestamp=1566444294344&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/environment" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/types" "^24.9.0"
    "jest-mock" "^24.9.0"
    "jest-util" "^24.9.0"

"jest-get-type@^22.1.0":
  "integrity" "sha1-46hQTYR5NC3UQgI2syKGnxiQDOQ="
  "resolved" "https://registry.npm.taobao.org/jest-get-type/download/jest-get-type-22.4.3.tgz"
  "version" "22.4.3"

"jest-get-type@^24.9.0":
  "integrity" "sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4="
  "resolved" "https://registry.npm.taobao.org/jest-get-type/download/jest-get-type-24.9.0.tgz"
  "version" "24.9.0"

"jest-haste-map@^23.6.0":
  "integrity" "sha1-Lj65l4FMppbWKv2z8lKfW7yTXhY="
  "resolved" "https://registry.npm.taobao.org/jest-haste-map/download/jest-haste-map-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.1.11"
    "invariant" "^2.2.4"
    "jest-docblock" "^23.2.0"
    "jest-serializer" "^23.0.1"
    "jest-worker" "^23.2.0"
    "micromatch" "^2.3.11"
    "sane" "^2.0.0"

"jest-haste-map@^24.9.0":
  "integrity" "sha1-s4pdZCdJNOIfpBeump++t3zqrH0="
  "resolved" "https://registry.npm.taobao.org/jest-haste-map/download/jest-haste-map-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "anymatch" "^2.0.0"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.1.15"
    "invariant" "^2.2.4"
    "jest-serializer" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-worker" "^24.9.0"
    "micromatch" "^3.1.10"
    "sane" "^4.0.3"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^1.2.7"

"jest-jasmine2@^23.6.0":
  "integrity" "sha1-hA6Tf4SKbIY43yQ2CrhpzHGFkuA="
  "resolved" "https://registry.npm.taobao.org/jest-jasmine2/download/jest-jasmine2-23.6.0.tgz?cache=0&sync_timestamp=1566444304523&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-traverse" "^6.0.0"
    "chalk" "^2.0.1"
    "co" "^4.6.0"
    "expect" "^23.6.0"
    "is-generator-fn" "^1.0.0"
    "jest-diff" "^23.6.0"
    "jest-each" "^23.6.0"
    "jest-matcher-utils" "^23.6.0"
    "jest-message-util" "^23.4.0"
    "jest-snapshot" "^23.6.0"
    "jest-util" "^23.4.0"
    "pretty-format" "^23.6.0"

"jest-jasmine2@^24.9.0":
  "integrity" "sha1-H3sb0yQsF3TmKsq7NkbZavw75qA="
  "resolved" "https://registry.npm.taobao.org/jest-jasmine2/download/jest-jasmine2-24.9.0.tgz?cache=0&sync_timestamp=1566444304523&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "chalk" "^2.0.1"
    "co" "^4.6.0"
    "expect" "^24.9.0"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^24.9.0"
    "jest-matcher-utils" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-runtime" "^24.9.0"
    "jest-snapshot" "^24.9.0"
    "jest-util" "^24.9.0"
    "pretty-format" "^24.9.0"
    "throat" "^4.0.0"

"jest-leak-detector@^23.6.0":
  "integrity" "sha1-5CMP1CzzgaGhlxI3rVaJfefhcd4="
  "resolved" "https://registry.npm.taobao.org/jest-leak-detector/download/jest-leak-detector-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "pretty-format" "^23.6.0"

"jest-leak-detector@^24.9.0":
  "integrity" "sha1-tmXep8dxAMXE99/LFTtlzwfc+Wo="
  "resolved" "https://registry.npm.taobao.org/jest-leak-detector/download/jest-leak-detector-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "jest-get-type" "^24.9.0"
    "pretty-format" "^24.9.0"

"jest-matcher-utils@^23.6.0":
  "integrity" "sha1-cmvOoMUpQmGnQXr7baMYa0uMrIA="
  "resolved" "https://registry.npm.taobao.org/jest-matcher-utils/download/jest-matcher-utils-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "^2.0.1"
    "jest-get-type" "^22.1.0"
    "pretty-format" "^23.6.0"

"jest-matcher-utils@^24.9.0":
  "integrity" "sha1-9bNmHV5ijf/m3WUlHf2uDofDoHM="
  "resolved" "https://registry.npm.taobao.org/jest-matcher-utils/download/jest-matcher-utils-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "chalk" "^2.0.1"
    "jest-diff" "^24.9.0"
    "jest-get-type" "^24.9.0"
    "pretty-format" "^24.9.0"

"jest-message-util@^23.4.0":
  "integrity" "sha1-F2EMUJQjSVCNAaPR4L2iwHkIap8="
  "resolved" "https://registry.npm.taobao.org/jest-message-util/download/jest-message-util-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "@babel/code-frame" "^7.0.0-beta.35"
    "chalk" "^2.0.1"
    "micromatch" "^2.3.11"
    "slash" "^1.0.0"
    "stack-utils" "^1.0.1"

"jest-message-util@^24.9.0":
  "integrity" "sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM="
  "resolved" "https://registry.npm.taobao.org/jest-message-util/download/jest-message-util-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/stack-utils" "^1.0.1"
    "chalk" "^2.0.1"
    "micromatch" "^3.1.10"
    "slash" "^2.0.0"
    "stack-utils" "^1.0.1"

"jest-mock@^23.2.0":
  "integrity" "sha1-rRxg8p6HGdR8JuETgJi20YsmETQ="
  "resolved" "https://registry.npm.taobao.org/jest-mock/download/jest-mock-23.2.0.tgz"
  "version" "23.2.0"

"jest-mock@^24.9.0":
  "integrity" "sha1-wig1VB7jebkIZzrVEIeiGFwT8cY="
  "resolved" "https://registry.npm.taobao.org/jest-mock/download/jest-mock-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"

"jest-pnp-resolver@^1.2.1":
  "integrity" "sha1-7NrmBMB3p/vHDe+21RfDwciYkjo="
  "resolved" "https://registry.npm.taobao.org/jest-pnp-resolver/download/jest-pnp-resolver-1.2.1.tgz"
  "version" "1.2.1"

"jest-regex-util@^23.3.0":
  "integrity" "sha1-X4ZylUfCeFxAAs6qj4Sf6MpHG8U="
  "resolved" "https://registry.npm.taobao.org/jest-regex-util/download/jest-regex-util-23.3.0.tgz"
  "version" "23.3.0"

"jest-regex-util@^24.3.0", "jest-regex-util@^24.9.0":
  "integrity" "sha1-wT+zOAveIr9ldUMsST6o/jeWVjY="
  "resolved" "https://registry.npm.taobao.org/jest-regex-util/download/jest-regex-util-24.9.0.tgz"
  "version" "24.9.0"

"jest-resolve-dependencies@^23.6.0":
  "integrity" "sha1-tFJq8kyFQNmj+rECwVCBz1Cbcj0="
  "resolved" "https://registry.npm.taobao.org/jest-resolve-dependencies/download/jest-resolve-dependencies-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "jest-regex-util" "^23.3.0"
    "jest-snapshot" "^23.6.0"

"jest-resolve-dependencies@^24.9.0":
  "integrity" "sha1-rQVRmJWcTPuopPBmxnOj8HhlB6s="
  "resolved" "https://registry.npm.taobao.org/jest-resolve-dependencies/download/jest-resolve-dependencies-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "jest-regex-util" "^24.3.0"
    "jest-snapshot" "^24.9.0"

"jest-resolve@*", "jest-resolve@^23.6.0":
  "integrity" "sha1-zx0aJM5+57I9ZhwzuiFQ866/oK4="
  "resolved" "https://registry.npm.taobao.org/jest-resolve/download/jest-resolve-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "browser-resolve" "^1.11.3"
    "chalk" "^2.0.1"
    "realpath-native" "^1.0.0"

"jest-resolve@^24.9.0":
  "integrity" "sha1-3/BMdoevNMTdflJIktnPd+XRcyE="
  "resolved" "https://registry.npm.taobao.org/jest-resolve/download/jest-resolve-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "browser-resolve" "^1.11.3"
    "chalk" "^2.0.1"
    "jest-pnp-resolver" "^1.2.1"
    "realpath-native" "^1.1.0"

"jest-runner@^23.6.0":
  "integrity" "sha1-OJS9IZ/8Pzy5TcSKQXCi5vI6Wjg="
  "resolved" "https://registry.npm.taobao.org/jest-runner/download/jest-runner-23.6.0.tgz?cache=0&sync_timestamp=1566444299910&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runner%2Fdownload%2Fjest-runner-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "exit" "^0.1.2"
    "graceful-fs" "^4.1.11"
    "jest-config" "^23.6.0"
    "jest-docblock" "^23.2.0"
    "jest-haste-map" "^23.6.0"
    "jest-jasmine2" "^23.6.0"
    "jest-leak-detector" "^23.6.0"
    "jest-message-util" "^23.4.0"
    "jest-runtime" "^23.6.0"
    "jest-util" "^23.4.0"
    "jest-worker" "^23.2.0"
    "source-map-support" "^0.5.6"
    "throat" "^4.0.0"

"jest-runner@^24.9.0":
  "integrity" "sha1-V0+v29VEVcKzS0vfQ2WiOFf830I="
  "resolved" "https://registry.npm.taobao.org/jest-runner/download/jest-runner-24.9.0.tgz?cache=0&sync_timestamp=1566444299910&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runner%2Fdownload%2Fjest-runner-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.7.1"
    "@jest/environment" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "chalk" "^2.4.2"
    "exit" "^0.1.2"
    "graceful-fs" "^4.1.15"
    "jest-config" "^24.9.0"
    "jest-docblock" "^24.3.0"
    "jest-haste-map" "^24.9.0"
    "jest-jasmine2" "^24.9.0"
    "jest-leak-detector" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-resolve" "^24.9.0"
    "jest-runtime" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-worker" "^24.6.0"
    "source-map-support" "^0.5.6"
    "throat" "^4.0.0"

"jest-runtime@^23.6.0":
  "integrity" "sha1-BZ5YyKtEWRfNDg2ErCumjejyMII="
  "resolved" "https://registry.npm.taobao.org/jest-runtime/download/jest-runtime-23.6.0.tgz?cache=0&sync_timestamp=1566444299905&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runtime%2Fdownload%2Fjest-runtime-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-core" "^6.0.0"
    "babel-plugin-istanbul" "^4.1.6"
    "chalk" "^2.0.1"
    "convert-source-map" "^1.4.0"
    "exit" "^0.1.2"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.1.11"
    "jest-config" "^23.6.0"
    "jest-haste-map" "^23.6.0"
    "jest-message-util" "^23.4.0"
    "jest-regex-util" "^23.3.0"
    "jest-resolve" "^23.6.0"
    "jest-snapshot" "^23.6.0"
    "jest-util" "^23.4.0"
    "jest-validate" "^23.6.0"
    "micromatch" "^2.3.11"
    "realpath-native" "^1.0.0"
    "slash" "^1.0.0"
    "strip-bom" "3.0.0"
    "write-file-atomic" "^2.1.0"
    "yargs" "^11.0.0"

"jest-runtime@^24.9.0":
  "integrity" "sha1-nxRYOvak9zFKap2fAibhp4HI5Kw="
  "resolved" "https://registry.npm.taobao.org/jest-runtime/download/jest-runtime-24.9.0.tgz?cache=0&sync_timestamp=1566444299905&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runtime%2Fdownload%2Fjest-runtime-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.7.1"
    "@jest/environment" "^24.9.0"
    "@jest/source-map" "^24.3.0"
    "@jest/transform" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/yargs" "^13.0.0"
    "chalk" "^2.0.1"
    "exit" "^0.1.2"
    "glob" "^7.1.3"
    "graceful-fs" "^4.1.15"
    "jest-config" "^24.9.0"
    "jest-haste-map" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-mock" "^24.9.0"
    "jest-regex-util" "^24.3.0"
    "jest-resolve" "^24.9.0"
    "jest-snapshot" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-validate" "^24.9.0"
    "realpath-native" "^1.1.0"
    "slash" "^2.0.0"
    "strip-bom" "^3.0.0"
    "yargs" "^13.3.0"

"jest-serializer-vue@^2.0.2":
  "integrity" "sha1-sjjvKGNX7GtIBCG9RxRQUJh9WbM="
  "resolved" "https://registry.npm.taobao.org/jest-serializer-vue/download/jest-serializer-vue-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "pretty" "2.0.0"

"jest-serializer@^23.0.1":
  "integrity" "sha1-o3dq6zEekP6D+rnlM+hRAr0WQWU="
  "resolved" "https://registry.npm.taobao.org/jest-serializer/download/jest-serializer-23.0.1.tgz?cache=0&sync_timestamp=1566444323619&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-serializer%2Fdownload%2Fjest-serializer-23.0.1.tgz"
  "version" "23.0.1"

"jest-serializer@^24.9.0":
  "integrity" "sha1-5tfX75bTHouQeacUdUxdXFgojnM="
  "resolved" "https://registry.npm.taobao.org/jest-serializer/download/jest-serializer-24.9.0.tgz?cache=0&sync_timestamp=1566444323619&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-serializer%2Fdownload%2Fjest-serializer-24.9.0.tgz"
  "version" "24.9.0"

"jest-snapshot@^23.6.0":
  "integrity" "sha1-+cJiXRsYrNoB7C0rgmwM5YpaoXo="
  "resolved" "https://registry.npm.taobao.org/jest-snapshot/download/jest-snapshot-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-types" "^6.0.0"
    "chalk" "^2.0.1"
    "jest-diff" "^23.6.0"
    "jest-matcher-utils" "^23.6.0"
    "jest-message-util" "^23.4.0"
    "jest-resolve" "^23.6.0"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "pretty-format" "^23.6.0"
    "semver" "^5.5.0"

"jest-snapshot@^24.9.0":
  "integrity" "sha1-7I6cpPLsDFyHro+SXPl0l7DpUbo="
  "resolved" "https://registry.npm.taobao.org/jest-snapshot/download/jest-snapshot-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^24.9.0"
    "chalk" "^2.0.1"
    "expect" "^24.9.0"
    "jest-diff" "^24.9.0"
    "jest-get-type" "^24.9.0"
    "jest-matcher-utils" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-resolve" "^24.9.0"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "pretty-format" "^24.9.0"
    "semver" "^6.2.0"

"jest-transform-stub@^2.0.0":
  "integrity" "sha1-GQGLCFH3VolyFHpdYAdLVfAiWn0="
  "resolved" "https://registry.npm.taobao.org/jest-transform-stub/download/jest-transform-stub-2.0.0.tgz"
  "version" "2.0.0"

"jest-util@^23.4.0":
  "integrity" "sha1-TQY8uSe68KI4Mf9hvsLLv0l5NWE="
  "resolved" "https://registry.npm.taobao.org/jest-util/download/jest-util-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "callsites" "^2.0.0"
    "chalk" "^2.0.1"
    "graceful-fs" "^4.1.11"
    "is-ci" "^1.0.10"
    "jest-message-util" "^23.4.0"
    "mkdirp" "^0.5.1"
    "slash" "^1.0.0"
    "source-map" "^0.6.0"

"jest-util@^24.9.0":
  "integrity" "sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI="
  "resolved" "https://registry.npm.taobao.org/jest-util/download/jest-util-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/source-map" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "callsites" "^3.0.0"
    "chalk" "^2.0.1"
    "graceful-fs" "^4.1.15"
    "is-ci" "^2.0.0"
    "mkdirp" "^0.5.1"
    "slash" "^2.0.0"
    "source-map" "^0.6.0"

"jest-validate@^23.6.0":
  "integrity" "sha1-NnYfmdHtM/zUJbTkxVldYrZZdHQ="
  "resolved" "https://registry.npm.taobao.org/jest-validate/download/jest-validate-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "^2.0.1"
    "jest-get-type" "^22.1.0"
    "leven" "^2.1.0"
    "pretty-format" "^23.6.0"

"jest-validate@^24.9.0":
  "integrity" "sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks="
  "resolved" "https://registry.npm.taobao.org/jest-validate/download/jest-validate-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "camelcase" "^5.3.1"
    "chalk" "^2.0.1"
    "jest-get-type" "^24.9.0"
    "leven" "^3.1.0"
    "pretty-format" "^24.9.0"

"jest-watch-typeahead@0.2.1":
  "integrity" "sha1-bEDyMplspsOZd+kp6febGJ59h+Q="
  "resolved" "https://registry.npm.taobao.org/jest-watch-typeahead/download/jest-watch-typeahead-0.2.1.tgz?cache=0&sync_timestamp=1573349458412&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-watch-typeahead%2Fdownload%2Fjest-watch-typeahead-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.4.1"
    "jest-watcher" "^23.1.0"
    "slash" "^2.0.0"
    "string-length" "^2.0.0"
    "strip-ansi" "^5.0.0"

"jest-watcher@^23.1.0", "jest-watcher@^23.4.0":
  "integrity" "sha1-0uKM50+NrWxq/JIrksq+9u0FyRw="
  "resolved" "https://registry.npm.taobao.org/jest-watcher/download/jest-watcher-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.1"
    "string-length" "^2.0.0"

"jest-watcher@^24.9.0":
  "integrity" "sha1-S1bl0c7/AF9biOUo3Jr8jdTtKzs="
  "resolved" "https://registry.npm.taobao.org/jest-watcher/download/jest-watcher-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/yargs" "^13.0.0"
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.1"
    "jest-util" "^24.9.0"
    "string-length" "^2.0.0"

"jest-worker@^23.2.0":
  "integrity" "sha1-+vcGqNo2+uYOsmlXJX+ntdjqArk="
  "resolved" "https://registry.npm.taobao.org/jest-worker/download/jest-worker-23.2.0.tgz"
  "version" "23.2.0"
  dependencies:
    "merge-stream" "^1.0.1"

"jest-worker@^24.6.0", "jest-worker@^24.9.0":
  "integrity" "sha1-Xb/bWy0yLphWeJgjipaXvM5ns+U="
  "resolved" "https://registry.npm.taobao.org/jest-worker/download/jest-worker-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "merge-stream" "^2.0.0"
    "supports-color" "^6.1.0"

"jest@^23.6.0":
  "integrity" "sha1-rVg16SPr9uGeeh11KaQy7f7ngT0="
  "resolved" "https://registry.npm.taobao.org/jest/download/jest-23.6.0.tgz?cache=0&sync_timestamp=1566444314105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest%2Fdownload%2Fjest-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "import-local" "^1.0.0"
    "jest-cli" "^23.6.0"

"jest@^24.9.0", "jest@>=24 <25":
  "integrity" "sha1-mH0pDAWgi1LFYYjBAC42jtsAcXE="
  "resolved" "https://registry.npm.taobao.org/jest/download/jest-24.9.0.tgz?cache=0&sync_timestamp=1566444314105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest%2Fdownload%2Fjest-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "import-local" "^2.0.0"
    "jest-cli" "^24.9.0"

"joi@^11.1.1":
  "integrity" "sha1-9nSJdTe2JemsPQt+FgTIKK2RPMs="
  "resolved" "https://registry.npm.taobao.org/joi/download/joi-11.4.0.tgz"
  "version" "11.4.0"
  dependencies:
    "hoek" "4.x.x"
    "isemail" "3.x.x"
    "topo" "2.x.x"

"js-beautify@^1.6.12", "js-beautify@^1.6.14":
  "integrity" "sha1-iMkJnNZVlAKxJM+rGHVJNvinsXg="
  "resolved" "https://registry.npm.taobao.org/js-beautify/download/js-beautify-1.10.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-beautify%2Fdownload%2Fjs-beautify-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "config-chain" "^1.1.12"
    "editorconfig" "^0.15.3"
    "glob" "^7.1.3"
    "mkdirp" "~0.5.1"
    "nopt" "~4.0.1"

"js-cookie@^2.2.1":
  "integrity" "sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg="
  "resolved" "https://registry.npm.taobao.org/js-cookie/download/js-cookie-2.2.1.tgz"
  "version" "2.2.1"

"js-levenshtein@^1.1.3":
  "integrity" "sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0="
  "resolved" "https://registry.npm.taobao.org/js-levenshtein/download/js-levenshtein-1.1.6.tgz"
  "version" "1.1.6"

"js-message@1.0.5":
  "integrity" "sha1-IwDSSxrwjondCVvBpMnJz8uJLRU="
  "resolved" "https://registry.npm.taobao.org/js-message/download/js-message-1.0.5.tgz"
  "version" "1.0.5"

"js-queue@2.0.0":
  "integrity" "sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug="
  "resolved" "https://registry.npm.taobao.org/js-queue/download/js-queue-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "easy-stack" "^1.0.0"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "https://registry.npm.taobao.org/js-tokens/download/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-tokens@^3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "https://registry.npm.taobao.org/js-tokens/download/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-yaml@^3.13.1", "js-yaml@^3.7.0", "js-yaml@^3.9.1":
  "integrity" "sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc="
  "resolved" "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsdom@^11.5.1":
  "integrity" "sha1-GoDUDd03ih3lllbp5txaO6hle8g="
  "resolved" "https://registry.npm.taobao.org/jsdom/download/jsdom-11.12.0.tgz"
  "version" "11.12.0"
  dependencies:
    "abab" "^2.0.0"
    "acorn" "^5.5.3"
    "acorn-globals" "^4.1.0"
    "array-equal" "^1.0.0"
    "cssom" ">= 0.3.2 < 0.4.0"
    "cssstyle" "^1.0.0"
    "data-urls" "^1.0.0"
    "domexception" "^1.0.1"
    "escodegen" "^1.9.1"
    "html-encoding-sniffer" "^1.0.2"
    "left-pad" "^1.3.0"
    "nwsapi" "^2.0.7"
    "parse5" "4.0.0"
    "pn" "^1.1.0"
    "request" "^2.87.0"
    "request-promise-native" "^1.0.5"
    "sax" "^1.2.4"
    "symbol-tree" "^3.2.2"
    "tough-cookie" "^2.3.4"
    "w3c-hr-time" "^1.0.1"
    "webidl-conversions" "^4.0.2"
    "whatwg-encoding" "^1.0.3"
    "whatwg-mimetype" "^2.1.0"
    "whatwg-url" "^6.4.1"
    "ws" "^5.2.0"
    "xml-name-validator" "^3.0.0"

"jsesc@^1.3.0":
  "integrity" "sha1-RsP+yMGJKxKwgz25vHYiF226s0s="
  "resolved" "https://registry.npm.taobao.org/jsesc/download/jsesc-1.3.0.tgz"
  "version" "1.3.0"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "https://registry.npm.taobao.org/jsesc/download/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.1", "json-parse-better-errors@^1.0.2":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "https://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@^0.3.0":
  "integrity" "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="
  "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz"
  "version" "0.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.npm.taobao.org/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stable-stringify@^1.0.1":
  "integrity" "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8="
  "resolved" "https://registry.npm.taobao.org/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "jsonify" "~0.0.0"

"json-stringify-safe@~5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json3@^3.3.2":
  "integrity" "sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E="
  "resolved" "https://registry.npm.taobao.org/json3/download/json3-3.3.3.tgz"
  "version" "3.3.3"

"json5@^0.5.0":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz?cache=0&sync_timestamp=1570050623700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^0.5.1":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz?cache=0&sync_timestamp=1570050623700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-1.0.1.tgz?cache=0&sync_timestamp=1570050623700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.0", "json5@2.x":
  "integrity" "sha1-gbbLBOm6SW8ccAXQe0NoomOPkLY="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-2.1.1.tgz?cache=0&sync_timestamp=1570050623700&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "minimist" "^1.2.0"

"jsonfile@^4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "https://registry.npm.taobao.org/jsonfile/download/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonify@~0.0.0":
  "integrity" "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="
  "resolved" "https://registry.npm.taobao.org/jsonify/download/jsonify-0.0.0.tgz"
  "version" "0.0.0"

"jsprim@^1.2.2":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"killable@^1.0.1":
  "integrity" "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI="
  "resolved" "https://registry.npm.taobao.org/killable/download/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-ARRrNqYhjmTljzqNZt5df8b20FE="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-6.0.2.tgz"
  "version" "6.0.2"

"kleur@^2.0.1":
  "integrity" "sha1-twT0lE2V4lXQOPDLBfuKYCxVowA="
  "resolved" "https://registry.npm.taobao.org/kleur/download/kleur-2.0.2.tgz"
  "version" "2.0.2"

"kleur@^3.0.3":
  "integrity" "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4="
  "resolved" "https://registry.npm.taobao.org/kleur/download/kleur-3.0.3.tgz"
  "version" "3.0.3"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc="
  "resolved" "https://registry.npm.taobao.org/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "launch-editor" "^2.2.1"

"launch-editor@^2.2.1":
  "integrity" "sha1-hxtaPuOdZoD8wm03kwtu7aidsMo="
  "resolved" "https://registry.npm.taobao.org/launch-editor/download/launch-editor-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "chalk" "^2.3.0"
    "shell-quote" "^1.6.1"

"lazy-ass@1.6.0":
  "integrity" "sha1-eZllXoZGwX8In90YfRUNMyTVRRM="
  "resolved" "https://registry.npm.taobao.org/lazy-ass/download/lazy-ass-1.6.0.tgz"
  "version" "1.6.0"

"lcid@^2.0.0":
  "integrity" "sha1-bvXS32DlL4LrIopMNz6NHzlyU88="
  "resolved" "https://registry.npm.taobao.org/lcid/download/lcid-2.0.0.tgz?cache=0&sync_timestamp=1570088800527&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flcid%2Fdownload%2Flcid-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "invert-kv" "^2.0.0"

"left-pad@^1.3.0":
  "integrity" "sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4="
  "resolved" "https://registry.npm.taobao.org/left-pad/download/left-pad-1.3.0.tgz"
  "version" "1.3.0"

"leven@^2.1.0":
  "integrity" "sha1-wuep93IJTe6dNCAq6KzORoeHVYA="
  "resolved" "https://registry.npm.taobao.org/leven/download/leven-2.1.0.tgz"
  "version" "2.1.0"

"leven@^3.1.0":
  "integrity" "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I="
  "resolved" "https://registry.npm.taobao.org/leven/download/leven-3.1.0.tgz"
  "version" "3.1.0"

"levn@^0.3.0", "levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://registry.npm.taobao.org/levn/download/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"lines-and-columns@^1.1.6":
  "integrity" "sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA="
  "resolved" "https://registry.npm.taobao.org/lines-and-columns/download/lines-and-columns-1.1.6.tgz"
  "version" "1.1.6"

"listr-silent-renderer@^1.1.1":
  "integrity" "sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4="
  "resolved" "https://registry.npm.taobao.org/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz"
  "version" "1.1.1"

"listr-update-renderer@^0.2.0":
  "integrity" "sha1-yoDhd5tOcCZoB+ju0a1qvjmFUPk="
  "resolved" "https://registry.npm.taobao.org/listr-update-renderer/download/listr-update-renderer-0.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flistr-update-renderer%2Fdownload%2Flistr-update-renderer-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "chalk" "^1.1.3"
    "cli-truncate" "^0.2.1"
    "elegant-spinner" "^1.0.1"
    "figures" "^1.7.0"
    "indent-string" "^3.0.0"
    "log-symbols" "^1.0.2"
    "log-update" "^1.0.2"
    "strip-ansi" "^3.0.1"

"listr-verbose-renderer@^0.4.0":
  "integrity" "sha1-ggb0z21S3cWCfl/RSYng6WWTOjU="
  "resolved" "https://registry.npm.taobao.org/listr-verbose-renderer/download/listr-verbose-renderer-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "chalk" "^1.1.3"
    "cli-cursor" "^1.0.2"
    "date-fns" "^1.27.2"
    "figures" "^1.7.0"

"listr@0.12.0":
  "integrity" "sha1-a84sD1YD+klYDqF81qAMwOX6RRo="
  "resolved" "https://registry.npm.taobao.org/listr/download/listr-0.12.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flistr%2Fdownload%2Flistr-0.12.0.tgz"
  "version" "0.12.0"
  dependencies:
    "chalk" "^1.1.3"
    "cli-truncate" "^0.2.1"
    "figures" "^1.7.0"
    "indent-string" "^2.1.0"
    "is-promise" "^2.1.0"
    "is-stream" "^1.1.0"
    "listr-silent-renderer" "^1.1.1"
    "listr-update-renderer" "^0.2.0"
    "listr-verbose-renderer" "^0.4.0"
    "log-symbols" "^1.0.2"
    "log-update" "^1.0.2"
    "ora" "^0.2.3"
    "p-map" "^1.1.1"
    "rxjs" "^5.0.0-beta.11"
    "stream-to-observable" "^0.1.0"
    "strip-ansi" "^3.0.1"

"load-json-file@^1.0.0":
  "integrity" "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA="
  "resolved" "https://registry.npm.taobao.org/load-json-file/download/load-json-file-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"
    "strip-bom" "^2.0.0"

"load-json-file@^2.0.0":
  "integrity" "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg="
  "resolved" "https://registry.npm.taobao.org/load-json-file/download/load-json-file-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "strip-bom" "^3.0.0"

"load-json-file@^4.0.0":
  "integrity" "sha1-L19Fq5HjMhYjT9U62rZo607AmTs="
  "resolved" "https://registry.npm.taobao.org/load-json-file/download/load-json-file-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^4.0.0"
    "pify" "^3.0.0"
    "strip-bom" "^3.0.0"

"loader-fs-cache@^1.0.0":
  "integrity" "sha1-VM7fa3J+F3n9jwEgXwX26IcG8IY="
  "resolved" "https://registry.npm.taobao.org/loader-fs-cache/download/loader-fs-cache-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "0.5.1"

"loader-runner@^2.3.1", "loader-runner@^2.4.0":
  "integrity" "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="
  "resolved" "https://registry.npm.taobao.org/loader-runner/download/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.1", "loader-utils@^1.0.2", "loader-utils@^1.1.0", "loader-utils@^1.2.3":
  "integrity" "sha1-H/XcaRHJ8KBiUxpMBLYJQGEIwsc="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^2.0.0"
    "json5" "^1.0.1"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"locate-path@^3.0.0":
  "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash._reinterpolate@^3.0.0":
  "integrity" "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0="
  "resolved" "https://registry.npm.taobao.org/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz"
  "version" "3.0.0"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY="
  "resolved" "https://registry.npm.taobao.org/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash.defaultsdeep%2Fdownload%2Flodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.find@^4.6.0":
  "integrity" "sha1-ywcE1Hq3F4n/oN6Ll92Sb7iLE7E="
  "resolved" "https://registry.npmjs.org/lodash.find/-/lodash.find-4.6.0.tgz"
  "version" "4.6.0"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha1-hImxyw0p/4gZXM7KRI/21swpXDY="
  "resolved" "https://registry.npm.taobao.org/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw="
  "resolved" "https://registry.npm.taobao.org/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2", "lodash.memoize@4.x":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.once@^4.1.1":
  "integrity" "sha1-DdOXEhPHxW34gJd9UEyI+0cal6w="
  "resolved" "https://registry.npm.taobao.org/lodash.once/download/lodash.once-4.1.1.tgz"
  "version" "4.1.1"

"lodash.sortby@^4.7.0":
  "integrity" "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg="
  "resolved" "https://registry.npm.taobao.org/lodash.sortby/download/lodash.sortby-4.7.0.tgz"
  "version" "4.7.0"

"lodash.template@^4.4.0":
  "integrity" "sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks="
  "resolved" "https://registry.npm.taobao.org/lodash.template/download/lodash.template-4.5.0.tgz?cache=0&sync_timestamp=1562735319590&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash.template%2Fdownload%2Flodash.template-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "lodash._reinterpolate" "^3.0.0"
    "lodash.templatesettings" "^4.0.0"

"lodash.templatesettings@^4.0.0":
  "integrity" "sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM="
  "resolved" "https://registry.npm.taobao.org/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "lodash._reinterpolate" "^3.0.0"

"lodash.transform@^4.6.0":
  "integrity" "sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A="
  "resolved" "https://registry.npm.taobao.org/lodash.transform/download/lodash.transform-4.6.0.tgz"
  "version" "4.6.0"

"lodash.unescape@4.0.1":
  "integrity" "sha1-vyJJiGzlFM2hEvrpIYzcBlIR/Jw="
  "resolved" "https://registry.npm.taobao.org/lodash.unescape/download/lodash.unescape-4.0.1.tgz"
  "version" "4.0.1"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.10", "lodash@^4.17.11", "lodash@^4.17.13", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.3.0", "lodash@4.17.15":
  "integrity" "sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg="
  "resolved" "https://registry.npm.taobao.org/lodash/download/lodash-4.17.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash%2Fdownload%2Flodash-4.17.15.tgz"
  "version" "4.17.15"

"log-symbols@^1.0.2":
  "integrity" "sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg="
  "resolved" "https://registry.npm.taobao.org/log-symbols/download/log-symbols-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "chalk" "^1.0.0"

"log-symbols@^2.2.0", "log-symbols@2.2.0":
  "integrity" "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo="
  "resolved" "https://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"log-update@^1.0.2":
  "integrity" "sha1-GZKfZMQJPS0ucHWh2tivWcKWuNE="
  "resolved" "https://registry.npm.taobao.org/log-update/download/log-update-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "ansi-escapes" "^1.0.0"
    "cli-cursor" "^1.0.2"

"loglevel@^1.6.4":
  "integrity" "sha1-DuYwDMBY22s1UfocS/c7g7t3ExI="
  "resolved" "https://registry.npm.taobao.org/loglevel/download/loglevel-1.6.6.tgz"
  "version" "1.6.6"

"loose-envify@^1.0.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "https://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.0.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.2":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.5":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"make-dir@^1.0.0":
  "integrity" "sha1-ecEDO4BRW9bSTsmTPoYMp17ifww="
  "resolved" "https://registry.npm.taobao.org/make-dir/download/make-dir-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "pify" "^3.0.0"

"make-dir@^2.0.0", "make-dir@^2.1.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.npm.taobao.org/make-dir/download/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-error@1.x":
  "integrity" "sha1-7+ToH22yjK3WBccPKcgxtY73dsg="
  "resolved" "https://registry.npm.taobao.org/make-error/download/make-error-1.3.5.tgz"
  "version" "1.3.5"

"makeerror@1.0.x":
  "integrity" "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw="
  "resolved" "https://registry.npm.taobao.org/makeerror/download/makeerror-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "tmpl" "1.0.x"

"mamacro@^0.0.3":
  "integrity" "sha1-rSyVdhl8nxq/MI0Hh4Zb2XWj8+Q="
  "resolved" "https://registry.npm.taobao.org/mamacro/download/mamacro-0.0.3.tgz"
  "version" "0.0.3"

"map-age-cleaner@^0.1.1":
  "integrity" "sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo="
  "resolved" "https://registry.npm.taobao.org/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "p-defer" "^1.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"math-random@^1.0.1":
  "integrity" "sha1-XdaUPJOFSCZwFtTjTwV1gwgMUUw="
  "resolved" "https://registry.npm.taobao.org/math-random/download/math-random-1.0.4.tgz"
  "version" "1.0.4"

"md5.js@^1.3.4":
  "integrity" "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="
  "resolved" "https://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"md5@^2.3.0":
  "integrity" "sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g=="
  "resolved" "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "charenc" "0.0.2"
    "crypt" "0.0.2"
    "is-buffer" "~1.1.6"

"mdn-data@2.0.4":
  "integrity" "sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs="
  "resolved" "https://registry.npm.taobao.org/mdn-data/download/mdn-data-2.0.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"mem@^4.0.0":
  "integrity" "sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg="
  "resolved" "https://registry.npm.taobao.org/mem/download/mem-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "map-age-cleaner" "^0.1.1"
    "mimic-fn" "^2.0.0"
    "p-is-promise" "^2.0.0"

"memory-fs@^0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz?cache=0&sync_timestamp=1572571385403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemory-fs%2Fdownload%2Fmemory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"memory-fs@^0.5.0":
  "integrity" "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.5.0.tgz?cache=0&sync_timestamp=1572571385403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemory-fs%2Fdownload%2Fmemory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-source-map@^1.1.0":
  "integrity" "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY="
  "resolved" "https://registry.npm.taobao.org/merge-source-map/download/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^1.0.1":
  "integrity" "sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE="
  "resolved" "https://registry.npm.taobao.org/merge-stream/download/merge-stream-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "readable-stream" "^2.0.1"

"merge-stream@^2.0.0":
  "integrity" "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="
  "resolved" "https://registry.npm.taobao.org/merge-stream/download/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge@^1.2.0":
  "integrity" "sha1-OL6/gMMiCopIe2/Ps5QbsRcgwUU="
  "resolved" "https://registry.npm.taobao.org/merge/download/merge-1.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmerge%2Fdownload%2Fmerge-1.2.1.tgz"
  "version" "1.2.1"

"merge2@^1.2.3":
  "integrity" "sha1-WzZu6DsvFYLEj4fkfPGpNSEDyoE="
  "resolved" "https://registry.npm.taobao.org/merge2/download/merge2-1.3.0.tgz"
  "version" "1.3.0"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^2.3.11":
  "integrity" "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU="
  "resolved" "https://registry.npm.taobao.org/micromatch/download/micromatch-2.3.11.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-2.3.11.tgz"
  "version" "2.3.11"
  dependencies:
    "arr-diff" "^2.0.0"
    "array-unique" "^0.2.1"
    "braces" "^1.8.2"
    "expand-brackets" "^0.1.4"
    "extglob" "^0.3.1"
    "filename-regex" "^2.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.1"
    "kind-of" "^3.0.2"
    "normalize-path" "^2.0.1"
    "object.omit" "^2.0.0"
    "parse-glob" "^3.0.4"
    "regex-cache" "^0.4.2"

"micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"miller-rabin@^4.0.0":
  "integrity" "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="
  "resolved" "https://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.40.0 < 2", "mime-db@1.42.0":
  "integrity" "sha1-PiUpB7THrbkGWXtLZWNics+ee6w="
  "resolved" "https://registry.npm.taobao.org/mime-db/download/mime-db-1.42.0.tgz"
  "version" "1.42.0"

"mime-types@^2.1.12", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24":
  "integrity" "sha1-OXctRmIfk+KoCoVsU7hqYhVqZDc="
  "resolved" "https://registry.npm.taobao.org/mime-types/download/mime-types-2.1.25.tgz"
  "version" "2.1.25"
  dependencies:
    "mime-db" "1.42.0"

"mime@^2.0.3", "mime@^2.4.4":
  "integrity" "sha1-vXuRE1/GsBzePpuuM9ZZtj2IV+U="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-2.4.4.tgz"
  "version" "2.4.4"

"mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz?cache=0&sync_timestamp=1560442058146&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmimic-fn%2Fdownload%2Fmimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.0.0", "mimic-fn@^2.1.0":
  "integrity" "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="
  "resolved" "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.1.0.tgz?cache=0&sync_timestamp=1560442058146&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmimic-fn%2Fdownload%2Fmimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@^0.8.0":
  "integrity" "sha1-gdQexP5YxxOpatfHI82y0L1NcOE="
  "resolved" "https://registry.npm.taobao.org/mini-css-extract-plugin/download/mini-css-extract-plugin-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "normalize-url" "1.9.1"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "https://registry.npm.taobao.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.0", "minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://registry.npm.taobao.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.2", "minimatch@^3.0.3", "minimatch@^3.0.4":
  "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
  "resolved" "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.1", "minimist@^1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz"
  "version" "1.2.0"

"minimist@~0.0.1", "minimist@0.0.8":
  "integrity" "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="
  "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz"
  "version" "0.0.8"

"minimist@1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz"
  "version" "1.2.0"

"mississippi@^2.0.0":
  "integrity" "sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8="
  "resolved" "https://registry.npm.taobao.org/mississippi/download/mississippi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^2.0.1"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mississippi@^3.0.0":
  "integrity" "sha1-6goykfl+C16HdrNj1fChLZTGcCI="
  "resolved" "https://registry.npm.taobao.org/mississippi/download/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^3.0.0"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmixin-deep%2Fdownload%2Fmixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1", "mkdirp@~0.5.1", "mkdirp@0.5.1", "mkdirp@0.x":
  "integrity" "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM="
  "resolved" "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "minimist" "0.0.8"

"moment@^2.24.0", "moment@2.24.0":
  "integrity" "sha1-DQVdU/UFKqZTyfbraLtdEr9cK1s="
  "resolved" "https://registry.npm.taobao.org/moment/download/moment-2.24.0.tgz"
  "version" "2.24.0"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://registry.npm.taobao.org/move-concurrently/download/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz"
  "version" "2.1.1"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.npm.taobao.org/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha1-oOx72QVcQoL3kMPIL04o2zsxsik="
  "resolved" "https://registry.npm.taobao.org/multicast-dns/download/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mute-stream@0.0.7":
  "integrity" "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="
  "resolved" "https://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.7.tgz"
  "version" "0.0.7"

"mute-stream@0.0.8":
  "integrity" "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="
  "resolved" "https://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"mz@^2.4.0":
  "integrity" "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI="
  "resolved" "https://registry.npm.taobao.org/mz/download/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "https://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="
  "resolved" "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.5.0", "neo-async@^2.6.0", "neo-async@^2.6.1":
  "integrity" "sha1-rCetpmFn+ohJpq3dg39rGJrSCBw="
  "resolved" "https://registry.npm.taobao.org/neo-async/download/neo-async-2.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fneo-async%2Fdownload%2Fneo-async-2.6.1.tgz"
  "version" "2.6.1"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "https://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^2.2.0":
  "integrity" "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="
  "resolved" "https://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz?cache=0&sync_timestamp=1575601620810&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fno-case%2Fdownload%2Fno-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-cache@^4.1.1":
  "integrity" "sha1-79hHTe5O3sQTjN3tWA9VFlAPczQ="
  "resolved" "https://registry.npm.taobao.org/node-cache/download/node-cache-4.2.1.tgz?cache=0&sync_timestamp=1574034933748&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-cache%2Fdownload%2Fnode-cache-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "clone" "2.x"
    "lodash" "^4.17.15"

"node-forge@0.9.0":
  "integrity" "sha1-1iQFDtu0SHStyhK7mlLsY8t4JXk="
  "resolved" "https://registry.npm.taobao.org/node-forge/download/node-forge-0.9.0.tgz"
  "version" "0.9.0"

"node-int64@^0.4.0":
  "integrity" "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="
  "resolved" "https://registry.npm.taobao.org/node-int64/download/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-ipc@^9.1.1":
  "integrity" "sha1-TiRe1pOOZRAOWV68XcNLFujdXWk="
  "resolved" "https://registry.npm.taobao.org/node-ipc/download/node-ipc-9.1.1.tgz"
  "version" "9.1.1"
  dependencies:
    "event-pubsub" "4.3.0"
    "js-message" "1.0.5"
    "js-queue" "2.0.0"

"node-libs-browser@^2.2.1":
  "integrity" "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU="
  "resolved" "https://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-modules-regexp@^1.0.0":
  "integrity" "sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA="
  "resolved" "https://registry.npm.taobao.org/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz"
  "version" "1.0.0"

"node-notifier@^5.2.1", "node-notifier@^5.4.2":
  "integrity" "sha1-y3La+UyTkECY4oucWQ/YZuRkvVA="
  "resolved" "https://registry.npm.taobao.org/node-notifier/download/node-notifier-5.4.3.tgz"
  "version" "5.4.3"
  dependencies:
    "growly" "^1.3.0"
    "is-wsl" "^1.1.0"
    "semver" "^5.5.0"
    "shellwords" "^0.1.1"
    "which" "^1.3.0"

"node-releases@^1.1.42":
  "integrity" "sha1-qZn2pi+HRpgfbakGJ6jS/AkLutc="
  "resolved" "https://registry.npm.taobao.org/node-releases/download/node-releases-1.1.42.tgz"
  "version" "1.1.42"
  dependencies:
    "semver" "^6.3.0"

"nopt@~4.0.1":
  "integrity" "sha1-0NRoWv1UFRk8jHUFYC0NF81kR00="
  "resolved" "https://registry.npm.taobao.org/nopt/download/nopt-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "abbrev" "1"
    "osenv" "^0.1.4"

"normalize-package-data@^2.3.2", "normalize-package-data@^2.5.0":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "https://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha1-MtDkcvkf80VwHBWoMRAY07CpA3k="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^2.0.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "https://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^3.0.0":
  "integrity" "sha1-suHE3E98bVd0PfczpPWXjRhlBVk="
  "resolved" "https://registry.npm.taobao.org/normalize-url/download/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"normalize-url@1.9.1":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://registry.npm.taobao.org/normalize-url/download/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize-wheel@^1.0.1":
  "integrity" "sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU="
  "resolved" "https://registry.npm.taobao.org/normalize-wheel/download/normalize-wheel-1.0.1.tgz"
  "version" "1.0.1"

"normalize.css@^8.0.1":
  "integrity" "sha1-m5iiCHOLnMJjTKrLxC0THJdIe/M="
  "resolved" "https://registry.npm.taobao.org/normalize.css/download/normalize.css-8.0.1.tgz"
  "version" "8.0.1"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.0":
  "integrity" "sha1-1kTsG9BWkYfSpSkJlxAjoKWOhDg="
  "resolved" "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "path-key" "^3.0.0"

"nprogress@^0.2.0":
  "integrity" "sha1-y480xTIT2JVyP8urkH6UIq28r7E="
  "resolved" "https://registry.npm.taobao.org/nprogress/download/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"nth-check@^1.0.2", "nth-check@~1.0.1":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "https://registry.npm.taobao.org/nth-check/download/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "https://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"nwsapi@^2.0.7":
  "integrity" "sha1-IEh5qePQaP8qVROcLHcngGgaOLc="
  "resolved" "https://registry.npm.taobao.org/nwsapi/download/nwsapi-2.2.0.tgz?cache=0&sync_timestamp=1572824577484&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnwsapi%2Fdownload%2Fnwsapi-2.2.0.tgz"
  "version" "2.2.0"

"oauth-sign@~0.9.0":
  "integrity" "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="
  "resolved" "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8="
  "resolved" "https://registry.npm.taobao.org/object-hash/download/object-hash-1.3.1.tgz?cache=0&sync_timestamp=1575158231507&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-hash%2Fdownload%2Fobject-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@^1.7.0":
  "integrity" "sha1-9Pa9GBrXfwBrXs5gvQtvOY/3Smc="
  "resolved" "https://registry.npm.taobao.org/object-inspect/download/object-inspect-1.7.0.tgz"
  "version" "1.7.0"

"object-is@^1.0.1":
  "integrity" "sha1-CqYOyZiaCz7Xlc9NBvYs8a1lObY="
  "resolved" "https://registry.npm.taobao.org/object-is/download/object-is-1.0.1.tgz"
  "version" "1.0.1"

"object-keys@^1.0.11", "object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "https://registry.npm.taobao.org/object-keys/download/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0":
  "integrity" "sha1-lovxEA15Vrs8oIbwBvhGs7xACNo="
  "resolved" "https://registry.npm.taobao.org/object.assign/download/object.assign-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "define-properties" "^1.1.2"
    "function-bind" "^1.1.1"
    "has-symbols" "^1.0.0"
    "object-keys" "^1.0.11"

"object.getownpropertydescriptors@^2.0.3":
  "integrity" "sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY="
  "resolved" "https://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "define-properties" "^1.1.2"
    "es-abstract" "^1.5.1"

"object.omit@^2.0.0":
  "integrity" "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo="
  "resolved" "https://registry.npm.taobao.org/object.omit/download/object.omit-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "for-own" "^0.1.4"
    "is-extendable" "^0.1.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0":
  "integrity" "sha1-v2gQ712j5TJXkOqqK+IT6oRiTak="
  "resolved" "https://registry.npm.taobao.org/object.values/download/object.values-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.12.0"
    "function-bind" "^1.1.1"
    "has" "^1.0.3"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="
  "resolved" "https://registry.npm.taobao.org/obuf/download/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "https://registry.npm.taobao.org/on-headers/download/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npm.taobao.org/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^1.0.0":
  "integrity" "sha1-ofeDj4MUxRbwXs78vEzP4EtO14k="
  "resolved" "https://registry.npm.taobao.org/onetime/download/onetime-1.1.0.tgz"
  "version" "1.1.0"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0":
  "integrity" "sha1-//DzyRYX/mK7UBiWNumayKbfe+U="
  "resolved" "https://registry.npm.taobao.org/onetime/download/onetime-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^6.3.0":
  "integrity" "sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk="
  "resolved" "https://registry.npm.taobao.org/open/download/open-6.4.0.tgz?cache=0&sync_timestamp=1571165001235&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopen%2Fdownload%2Fopen-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"opener@^1.5.1":
  "integrity" "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0="
  "resolved" "https://registry.npm.taobao.org/opener/download/opener-1.5.1.tgz"
  "version" "1.5.1"

"opn@^5.5.0":
  "integrity" "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w="
  "resolved" "https://registry.npm.taobao.org/opn/download/opn-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopn%2Fdownload%2Fopn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optimist@^0.6.1":
  "integrity" "sha1-2j6nRob6IaGaERwybpDrFaAZZoY="
  "resolved" "https://registry.npm.taobao.org/optimist/download/optimist-0.6.1.tgz"
  "version" "0.6.1"
  dependencies:
    "minimist" "~0.0.1"
    "wordwrap" "~0.0.2"

"optionator@^0.8.1", "optionator@^0.8.2", "optionator@^0.8.3":
  "integrity" "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU="
  "resolved" "https://registry.npm.taobao.org/optionator/download/optionator-0.8.3.tgz?cache=0&sync_timestamp=1573077370127&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Foptionator%2Fdownload%2Foptionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"ora@^0.2.3":
  "integrity" "sha1-N1J9Igrc1Tw5tzVx11QVbV22V6Q="
  "resolved" "https://registry.npm.taobao.org/ora/download/ora-0.2.3.tgz?cache=0&sync_timestamp=1573640718234&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fora%2Fdownload%2Fora-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "chalk" "^1.1.1"
    "cli-cursor" "^1.0.2"
    "cli-spinners" "^0.1.2"
    "object-assign" "^4.0.1"

"ora@^3.4.0":
  "integrity" "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg="
  "resolved" "https://registry.npm.taobao.org/ora/download/ora-3.4.0.tgz?cache=0&sync_timestamp=1573640718234&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fora%2Fdownload%2Fora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"original@^1.0.0":
  "integrity" "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8="
  "resolved" "https://registry.npm.taobao.org/original/download/original-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "url-parse" "^1.4.3"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://registry.npm.taobao.org/os-browserify/download/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-homedir@^1.0.0", "os-homedir@^1.0.1":
  "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
  "resolved" "https://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz"
  "version" "1.0.2"

"os-locale@^3.0.0", "os-locale@^3.1.0":
  "integrity" "sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo="
  "resolved" "https://registry.npm.taobao.org/os-locale/download/os-locale-3.1.0.tgz?cache=0&sync_timestamp=1560274285880&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fos-locale%2Fdownload%2Fos-locale-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "execa" "^1.0.0"
    "lcid" "^2.0.0"
    "mem" "^4.0.0"

"os-tmpdir@^1.0.0", "os-tmpdir@^1.0.1", "os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"osenv@^0.1.4":
  "integrity" "sha1-hc36+uso6Gd/QW4odZK18/SepBA="
  "resolved" "https://registry.npm.taobao.org/osenv/download/osenv-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.0"

"p-defer@^1.0.0":
  "integrity" "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww="
  "resolved" "https://registry.npm.taobao.org/p-defer/download/p-defer-1.0.0.tgz"
  "version" "1.0.0"

"p-each-series@^1.0.0":
  "integrity" "sha1-kw89Et0fUOdDRFeiLNbwSsatf3E="
  "resolved" "https://registry.npm.taobao.org/p-each-series/download/p-each-series-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "p-reduce" "^1.0.0"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz?cache=0&sync_timestamp=1560955759606&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-finally%2Fdownload%2Fp-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@^2.0.0":
  "integrity" "sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE="
  "resolved" "https://registry.npm.taobao.org/p-finally/download/p-finally-2.0.1.tgz?cache=0&sync_timestamp=1560955759606&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-finally%2Fdownload%2Fp-finally-2.0.1.tgz"
  "version" "2.0.1"

"p-is-promise@^2.0.0":
  "integrity" "sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4="
  "resolved" "https://registry.npm.taobao.org/p-is-promise/download/p-is-promise-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-is-promise%2Fdownload%2Fp-is-promise-2.1.0.tgz"
  "version" "2.1.0"

"p-limit@^1.0.0", "p-limit@^1.1.0":
  "integrity" "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg="
  "resolved" "https://registry.npm.taobao.org/p-limit/download/p-limit-1.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^2.0.0", "p-limit@^2.2.0":
  "integrity" "sha1-qgeniMwxUck5tRMfY1cPDdIAlTc="
  "resolved" "https://registry.npm.taobao.org/p-limit/download/p-limit-2.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-locate@^3.0.0":
  "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
  "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^1.1.1":
  "integrity" "sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s="
  "resolved" "https://registry.npm.taobao.org/p-map/download/p-map-1.2.0.tgz"
  "version" "1.2.0"

"p-map@^2.0.0":
  "integrity" "sha1-MQko/u+cnsxltosXaTAYpmXOoXU="
  "resolved" "https://registry.npm.taobao.org/p-map/download/p-map-2.1.0.tgz"
  "version" "2.1.0"

"p-reduce@^1.0.0":
  "integrity" "sha1-GMKw3ZNqRpClKfgjH1ig/bakffo="
  "resolved" "https://registry.npm.taobao.org/p-reduce/download/p-reduce-1.0.0.tgz"
  "version" "1.0.0"

"p-retry@^3.0.1":
  "integrity" "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg="
  "resolved" "https://registry.npm.taobao.org/p-retry/download/p-retry-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "retry" "^0.12.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.npm.taobao.org/p-try/download/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "https://registry.npm.taobao.org/p-try/download/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.5":
  "integrity" "sha1-Qyi621CGpCaqkPVBl31JVdpclzI="
  "resolved" "https://registry.npm.taobao.org/pako/download/pako-1.0.10.tgz"
  "version" "1.0.10"

"parallel-transform@^1.1.0":
  "integrity" "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw="
  "resolved" "https://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz?cache=0&sync_timestamp=1575601623421&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparam-case%2Fdownload%2Fparam-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI="
  "resolved" "https://registry.npm.taobao.org/parent-module/download/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-asn1@^5.0.0":
  "integrity" "sha1-ADJxND2ljclMrOSU+u89IUfs6g4="
  "resolved" "https://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "asn1.js" "^4.0.0"
    "browserify-aes" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-glob@^3.0.4":
  "integrity" "sha1-ssN2z7EfNVE7rdFz7wu246OIORw="
  "resolved" "https://registry.npm.taobao.org/parse-glob/download/parse-glob-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "glob-base" "^0.3.0"
    "is-dotfile" "^1.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.0"

"parse-json@^2.2.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "https://registry.npm.taobao.org/parse-json/download/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "^1.2.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://registry.npm.taobao.org/parse-json/download/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha1-c+URTJhtFD76NxLU6iTbmkJm9g8="
  "resolved" "https://registry.npm.taobao.org/parse-json/download/parse-json-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^5.1.1":
  "integrity" "sha1-6MdD1OkhlNUpPs3isIvjHmdGHLw="
  "resolved" "https://registry.npm.taobao.org/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "parse5" "^5.1.1"

"parse5@^5.1.1":
  "integrity" "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg="
  "resolved" "https://registry.npm.taobao.org/parse5/download/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@4.0.0":
  "integrity" "sha1-bXhlbj2o14tOwLkG98CO8d/j9gg="
  "resolved" "https://registry.npm.taobao.org/parse5/download/parse5-4.0.0.tgz"
  "version" "4.0.0"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "https://registry.npm.taobao.org/parseurl/download/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz?cache=0&sync_timestamp=1565253337239&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpascalcase%2Fdownload%2Fpascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha1-5sTd1+06onxoogzE5Q4aTug7vEo="
  "resolved" "https://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0", "path-is-absolute@^1.0.1":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.1", "path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1574442298523&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://registry.npm.taobao.org/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1574442298523&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.5", "path-parse@^1.0.6":
  "integrity" "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="
  "resolved" "https://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz"
  "version" "1.0.6"

"path-to-regexp@^3.0.0":
  "integrity" "sha1-+nh37LxJXGAZB1YiIkU8Q8wgSl8="
  "resolved" "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-3.2.0.tgz"
  "version" "3.2.0"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^1.0.0":
  "integrity" "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"path-type@^2.0.0":
  "integrity" "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pify" "^2.0.0"

"path-type@^3.0.0":
  "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha1-l2wgZTBhexTrsyEUI597CTNuk6Y="
  "resolved" "https://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.0.17.tgz"
  "version" "3.0.17"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"pend@~1.2.0":
  "integrity" "sha1-elfrVQpng/kRUzH89GY9XI4AelA="
  "resolved" "https://registry.npm.taobao.org/pend/download/pend-1.2.0.tgz"
  "version" "1.2.0"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picomatch@^2.0.4":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^2.2.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pirates@^4.0.1":
  "integrity" "sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c="
  "resolved" "https://registry.npm.taobao.org/pirates/download/pirates-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "node-modules-regexp" "^1.0.0"

"pkg-dir@^1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pkg-dir@^3.0.0":
  "integrity" "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="
  "resolved" "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-up@^2.0.0":
  "integrity" "sha1-yBmscoBZpGHKscOImivjxJoATX8="
  "resolved" "https://registry.npm.taobao.org/pkg-up/download/pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pluralize@^7.0.0":
  "integrity" "sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c="
  "resolved" "https://registry.npm.taobao.org/pluralize/download/pluralize-7.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpluralize%2Fdownload%2Fpluralize-7.0.0.tgz"
  "version" "7.0.0"

"pn@^1.1.0":
  "integrity" "sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs="
  "resolved" "https://registry.npm.taobao.org/pn/download/pn-1.1.0.tgz"
  "version" "1.1.0"

"portfinder@^1.0.20", "portfinder@^1.0.25":
  "integrity" "sha1-JU/TN/+6hp9LnTftwpgFnLTTXso="
  "resolved" "https://registry.npm.taobao.org/portfinder/download/portfinder-1.0.25.tgz"
  "version" "1.0.25"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.1"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^7.0.1":
  "integrity" "sha1-Ntd7qwI7Dsu5eJ2E3LI8SUEUVDY="
  "resolved" "https://registry.npm.taobao.org/postcss-calc/download/postcss-calc-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "css-unit-converter" "^1.1.1"
    "postcss" "^7.0.5"
    "postcss-selector-parser" "^5.0.0-rc.4"
    "postcss-value-parser" "^3.3.1"

"postcss-colormin@^4.0.3":
  "integrity" "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E="
  "resolved" "https://registry.npm.taobao.org/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-convert-values@^4.0.1":
  "integrity" "sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8="
  "resolved" "https://registry.npm.taobao.org/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM="
  "resolved" "https://registry.npm.taobao.org/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha1-P+EzzTyCKC5VD8myORdqkge3hOs="
  "resolved" "https://registry.npm.taobao.org/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha1-yMlR6fc+2UKAGUWERKAq2Qu592U="
  "resolved" "https://registry.npm.taobao.org/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha1-ZSrvipZybwKfXj4AFG7npOdV/1c="
  "resolved" "https://registry.npm.taobao.org/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha1-yE1pK3u3tB3c7ZTuYuirMbQXsAM="
  "resolved" "https://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-loader@^3.0.0":
  "integrity" "sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0="
  "resolved" "https://registry.npm.taobao.org/postcss-loader/download/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^7.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^1.0.0"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ="
  "resolved" "https://registry.npm.taobao.org/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha1-NivqT/Wh+Y5AdacTxsslrv75plA="
  "resolved" "https://registry.npm.taobao.org/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY="
  "resolved" "https://registry.npm.taobao.org/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE="
  "resolved" "https://registry.npm.taobao.org/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ="
  "resolved" "https://registry.npm.taobao.org/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g="
  "resolved" "https://registry.npm.taobao.org/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^1.2.0":
  "integrity" "sha1-3IfjQUjsfqtfeR981YSYMzdbdBo="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-extract-imports%2Fdownload%2Fpostcss-modules-extract-imports-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "postcss" "^6.0.1"

"postcss-modules-local-by-default@^1.2.0":
  "integrity" "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz?cache=0&sync_timestamp=1559685933515&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-scope@^1.1.0":
  "integrity" "sha1-1upkmUx5+XtipytCb75gVqGUu5A="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-values@^1.3.0":
  "integrity" "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "icss-replace-symbols" "^1.1.0"
    "postcss" "^6.0.1"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha1-izWt067oOhNrBHHg1ZvlilAoXdQ="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha1-jgCcoqOUnNr4rSPmtquZy159KNk="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI="
  "resolved" "https://registry.npm.taobao.org/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4="
  "resolved" "https://registry.npm.taobao.org/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8="
  "resolved" "https://registry.npm.taobao.org/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha1-F++kBerMbge+NBSlyi0QdGgdTik="
  "resolved" "https://registry.npm.taobao.org/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha1-T4dfSvsMllc9XPTXQBGu4lCn6GU="
  "resolved" "https://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "dot-prop" "^4.1.1"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^5.0.0", "postcss-selector-parser@^5.0.0-rc.4":
  "integrity" "sha1-JJBENWaXsztk8aj3yAki3d7nGVw="
  "resolved" "https://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "cssesc" "^2.0.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-svgo@^4.0.2":
  "integrity" "sha1-F7mXvHEbMzurFDqu07jT1uPTglg="
  "resolved" "https://registry.npm.taobao.org/postcss-svgo/download/postcss-svgo-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "is-svg" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w="
  "resolved" "https://registry.npm.taobao.org/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.0", "postcss-value-parser@^3.3.0", "postcss-value-parser@^3.3.1":
  "integrity" "sha1-n/giVH4okyE88cMO+lGsX9G6goE="
  "resolved" "https://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.2":
  "integrity" "sha1-SCKCwJpCcG0fyaBptz9E7Ag5Hck="
  "resolved" "https://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-4.0.2.tgz"
  "version" "4.0.2"

"postcss@^6.0.1":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "https://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz?cache=0&sync_timestamp=1574116615588&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^6.0.23":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "https://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz?cache=0&sync_timestamp=1574116615588&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^7.0.0", "postcss@^7.0.1", "postcss@^7.0.14", "postcss@^7.0.23", "postcss@^7.0.5":
  "integrity" "sha1-n5dZ+tZhsVlk88/DFA9m8eBercE="
  "resolved" "https://registry.npm.taobao.org/postcss/download/postcss-7.0.23.tgz?cache=0&sync_timestamp=1574116615588&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.23.tgz"
  "version" "7.0.23"
  dependencies:
    "chalk" "^2.4.2"
    "source-map" "^0.6.1"
    "supports-color" "^6.1.0"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"preserve@^0.2.0":
  "integrity" "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks="
  "resolved" "https://registry.npm.taobao.org/preserve/download/preserve-0.2.0.tgz"
  "version" "0.2.0"

"prettier@^1.18.2":
  "integrity" "sha1-99f1/4qc2HKnvkyhQglZVqYHl8s="
  "resolved" "https://registry.npm.taobao.org/prettier/download/prettier-1.19.1.tgz"
  "version" "1.19.1"

"pretty-bytes@^4.0.2":
  "integrity" "sha1-sr+C5zUNZcbDOqlaqlpPYyf2HNk="
  "resolved" "https://registry.npm.taobao.org/pretty-bytes/download/pretty-bytes-4.0.2.tgz"
  "version" "4.0.2"

"pretty-error@^2.0.2":
  "integrity" "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM="
  "resolved" "https://registry.npm.taobao.org/pretty-error/download/pretty-error-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "renderkid" "^2.0.1"
    "utila" "~0.4"

"pretty-format@^23.6.0":
  "integrity" "sha1-XqrI7razO5h7f+YJfqaooUarV2A="
  "resolved" "https://registry.npm.taobao.org/pretty-format/download/pretty-format-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "ansi-regex" "^3.0.0"
    "ansi-styles" "^3.2.0"

"pretty-format@^24.9.0":
  "integrity" "sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k="
  "resolved" "https://registry.npm.taobao.org/pretty-format/download/pretty-format-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "ansi-regex" "^4.0.0"
    "ansi-styles" "^3.2.0"
    "react-is" "^16.8.4"

"pretty@^2.0.0", "pretty@2.0.0":
  "integrity" "sha1-rbx5YLe7/iiaVX3F9zdhmiINBqU="
  "resolved" "https://registry.npm.taobao.org/pretty/download/pretty-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpretty%2Fdownload%2Fpretty-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "condense-newlines" "^0.2.1"
    "extend-shallow" "^2.0.1"
    "js-beautify" "^1.6.12"

"private@^0.1.6", "private@^0.1.8":
  "integrity" "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="
  "resolved" "https://registry.npm.taobao.org/private/download/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprocess-nextick-args%2Fdownload%2Fprocess-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://registry.npm.taobao.org/process/download/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^2.0.0":
  "integrity" "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="
  "resolved" "https://registry.npm.taobao.org/progress/download/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://registry.npm.taobao.org/promise-inflight/download/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"prompts@^0.1.9":
  "integrity" "sha1-qOFcYSxcnsj4ERhH3zM3ycvUQ7I="
  "resolved" "https://registry.npm.taobao.org/prompts/download/prompts-0.1.14.tgz?cache=0&sync_timestamp=1573411519291&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprompts%2Fdownload%2Fprompts-0.1.14.tgz"
  "version" "0.1.14"
  dependencies:
    "kleur" "^2.0.1"
    "sisteransi" "^0.1.1"

"prompts@^2.0.1":
  "integrity" "sha1-pETpaPpMx+hmiadAUGhayABsTMQ="
  "resolved" "https://registry.npm.taobao.org/prompts/download/prompts-2.3.0.tgz?cache=0&sync_timestamp=1573411519291&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprompts%2Fdownload%2Fprompts-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "kleur" "^3.0.3"
    "sisteransi" "^1.0.3"

"proto-list@~1.2.1":
  "integrity" "sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk="
  "resolved" "https://registry.npm.taobao.org/proto-list/download/proto-list-1.2.4.tgz"
  "version" "1.2.4"

"proxy-addr@~2.0.5":
  "integrity" "sha1-NMvWSi2B9LH9IedvnwbIpFKZ7jQ="
  "resolved" "https://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "forwarded" "~0.1.2"
    "ipaddr.js" "1.9.0"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.24":
  "integrity" "sha1-YFV1gu4jtsQ3GdmJD7QXDs2R4RA="
  "resolved" "https://registry.npm.taobao.org/psl/download/psl-1.6.0.tgz?cache=0&sync_timestamp=1575592019162&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpsl%2Fdownload%2Fpsl-1.6.0.tgz"
  "version" "1.6.0"

"public-encrypt@^4.0.0":
  "integrity" "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="
  "resolved" "https://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "https://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^2.0.1":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "https://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "https://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="
  "resolved" "https://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^1.4.1":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@2.x.x":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.npm.taobao.org/q/download/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@~6.5.2":
  "integrity" "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="
  "resolved" "https://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz"
  "version" "6.5.2"

"qs@6.7.0":
  "integrity" "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="
  "resolved" "https://registry.npm.taobao.org/qs/download/qs-6.7.0.tgz"
  "version" "6.7.0"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://registry.npm.taobao.org/query-string/download/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha1-YOWl/WSn+L+k0qsu1v30yFutFU4="
  "resolved" "https://registry.npm.taobao.org/querystringify/download/querystringify-2.1.1.tgz"
  "version" "2.1.1"

"ramda@0.24.1":
  "integrity" "sha1-w7d1UZfzW43DUCIoJixMkd22uFc="
  "resolved" "https://registry.npm.taobao.org/ramda/download/ramda-0.24.1.tgz"
  "version" "0.24.1"

"randomatic@^3.0.0":
  "integrity" "sha1-t3bvxZN1mE42xTey9RofCv8Noe0="
  "resolved" "https://registry.npm.taobao.org/randomatic/download/randomatic-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "is-number" "^4.0.0"
    "kind-of" "^6.0.0"
    "math-random" "^1.0.1"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5":
  "integrity" "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="
  "resolved" "https://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="
  "resolved" "https://registry.npm.taobao.org/randomfill/download/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.0":
  "integrity" "sha1-oc5vucm8NWylLoklarWQWeE9AzI="
  "resolved" "https://registry.npm.taobao.org/raw-body/download/raw-body-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"react-is@^16.8.4":
  "integrity" "sha1-LMD+D7p0LZf9UnxCoTvsTusGJBw="
  "resolved" "https://registry.npm.taobao.org/react-is/download/react-is-16.12.0.tgz?cache=0&sync_timestamp=1574323587913&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freact-is%2Fdownload%2Freact-is-16.12.0.tgz"
  "version" "16.12.0"

"read-pkg-up@^1.0.1":
  "integrity" "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI="
  "resolved" "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-1.0.1.tgz?cache=0&sync_timestamp=1575620465504&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-up" "^1.0.0"
    "read-pkg" "^1.0.0"

"read-pkg-up@^2.0.0":
  "integrity" "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4="
  "resolved" "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-2.0.0.tgz?cache=0&sync_timestamp=1575620465504&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.0.0"
    "read-pkg" "^2.0.0"

"read-pkg-up@^4.0.0":
  "integrity" "sha1-GyIcYIi6d5lgHICPkRYcZuWPiXg="
  "resolved" "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-4.0.0.tgz?cache=0&sync_timestamp=1575620465504&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "find-up" "^3.0.0"
    "read-pkg" "^3.0.0"

"read-pkg@^1.0.0":
  "integrity" "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg="
  "resolved" "https://registry.npm.taobao.org/read-pkg/download/read-pkg-1.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "load-json-file" "^1.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^1.0.0"

"read-pkg@^2.0.0":
  "integrity" "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg="
  "resolved" "https://registry.npm.taobao.org/read-pkg/download/read-pkg-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "load-json-file" "^2.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^2.0.0"

"read-pkg@^3.0.0":
  "integrity" "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k="
  "resolved" "https://registry.npm.taobao.org/read-pkg/download/read-pkg-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "load-json-file" "^4.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^3.0.0"

"read-pkg@^5.0.0":
  "integrity" "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w="
  "resolved" "https://registry.npm.taobao.org/read-pkg/download/read-pkg-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@1 || 2":
  "integrity" "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freadable-stream%2Fdownload%2Freadable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha1-pRwmdUZY4KPCHb9ZFjvUW6b0R/w="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freadable-stream%2Fdownload%2Freadable-stream-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.1.1":
  "integrity" "sha1-pRwmdUZY4KPCHb9ZFjvUW6b0R/w="
  "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freadable-stream%2Fdownload%2Freadable-stream-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.2.1":
  "integrity" "sha1-DodiKjMlqjPokihcr4tOhGUppSU="
  "resolved" "https://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz?cache=0&sync_timestamp=1571012694060&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"realpath-native@^1.0.0", "realpath-native@^1.1.0":
  "integrity" "sha1-IAMpT+oj+wZy8kduviL89Jii1lw="
  "resolved" "https://registry.npm.taobao.org/realpath-native/download/realpath-native-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "util.promisify" "^1.0.0"

"regenerate-unicode-properties@^8.1.0":
  "integrity" "sha1-71Hg8OpK1CS3e/fLQfPgFccKPw4="
  "resolved" "https://registry.npm.taobao.org/regenerate-unicode-properties/download/regenerate-unicode-properties-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "regenerate" "^1.4.0"

"regenerate@^1.2.1", "regenerate@^1.4.0":
  "integrity" "sha1-SoVuxLVuQHfFV1icroXnpMiGmhE="
  "resolved" "https://registry.npm.taobao.org/regenerate/download/regenerate-1.4.0.tgz"
  "version" "1.4.0"

"regenerator-runtime@^0.11.0":
  "integrity" "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="
  "resolved" "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.2":
  "integrity" "sha1-fPanfY9cb2Drc8X8GVWyzrAea/U="
  "resolved" "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.13.3.tgz"
  "version" "0.13.3"

"regenerator-transform@^0.14.0":
  "integrity" "sha1-Oy/OThq3cywI9mXf2zFHScfd0vs="
  "resolved" "https://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.14.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-transform%2Fdownload%2Fregenerator-transform-0.14.1.tgz"
  "version" "0.14.1"
  dependencies:
    "private" "^0.1.6"

"regex-cache@^0.4.2":
  "integrity" "sha1-db3FiioUls7EihKDW8VMjVYjNt0="
  "resolved" "https://registry.npm.taobao.org/regex-cache/download/regex-cache-0.4.4.tgz"
  "version" "0.4.4"
  dependencies:
    "is-equal-shallow" "^0.1.3"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "https://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0":
  "integrity" "sha1-azByTjBqJ4M+6xcbZqyIkLo35Bw="
  "resolved" "https://registry.npm.taobao.org/regexp.prototype.flags/download/regexp.prototype.flags-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "define-properties" "^1.1.2"

"regexpp@^1.0.1":
  "integrity" "sha1-DjUW3Qt5BPQT0tQZPc5GGMOmias="
  "resolved" "https://registry.npm.taobao.org/regexpp/download/regexpp-1.1.0.tgz"
  "version" "1.1.0"

"regexpp@^2.0.1":
  "integrity" "sha1-jRnTHPYySCtYkEn4KB+T28uk0H8="
  "resolved" "https://registry.npm.taobao.org/regexpp/download/regexpp-2.0.1.tgz"
  "version" "2.0.1"

"regexpu-core@^1.0.0":
  "integrity" "sha1-hqdj9Y7k18L2sQLkdkBQ3n7ZDGs="
  "resolved" "https://registry.npm.taobao.org/regexpu-core/download/regexpu-core-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regexpu-core@^4.6.0":
  "integrity" "sha1-IDfBizJ8/Oim/qKk7EQfJDKvuLY="
  "resolved" "https://registry.npm.taobao.org/regexpu-core/download/regexpu-core-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "regenerate" "^1.4.0"
    "regenerate-unicode-properties" "^8.1.0"
    "regjsgen" "^0.5.0"
    "regjsparser" "^0.6.0"
    "unicode-match-property-ecmascript" "^1.0.4"
    "unicode-match-property-value-ecmascript" "^1.1.0"

"register-service-worker@^1.6.2":
  "integrity" "sha1-kpflTCBcNxxuSb+oj2mX6N0xX0w="
  "resolved" "https://registry.npm.taobao.org/register-service-worker/download/register-service-worker-1.6.2.tgz"
  "version" "1.6.2"

"regjsgen@^0.2.0":
  "integrity" "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="
  "resolved" "https://registry.npm.taobao.org/regjsgen/download/regjsgen-0.2.0.tgz?cache=0&sync_timestamp=1571560526353&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregjsgen%2Fdownload%2Fregjsgen-0.2.0.tgz"
  "version" "0.2.0"

"regjsgen@^0.5.0":
  "integrity" "sha1-SPC/Gl6iBRlpKcDZeYtC0e2YRDw="
  "resolved" "https://registry.npm.taobao.org/regjsgen/download/regjsgen-0.5.1.tgz?cache=0&sync_timestamp=1571560526353&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregjsgen%2Fdownload%2Fregjsgen-0.5.1.tgz"
  "version" "0.5.1"

"regjsparser@^0.1.4":
  "integrity" "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw="
  "resolved" "https://registry.npm.taobao.org/regjsparser/download/regjsparser-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "jsesc" "~0.5.0"

"regjsparser@^0.6.0":
  "integrity" "sha1-8eaui32iuulsmTmbhozWyTOiupw="
  "resolved" "https://registry.npm.taobao.org/regjsparser/download/regjsparser-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.1":
  "integrity" "sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk="
  "resolved" "https://registry.npm.taobao.org/renderkid/download/renderkid-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "css-select" "^1.1.0"
    "dom-converter" "^0.2"
    "htmlparser2" "^3.3.0"
    "strip-ansi" "^3.0.0"
    "utila" "^0.4.0"

"repeat-element@^1.1.2":
  "integrity" "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4="
  "resolved" "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.3.tgz"
  "version" "1.1.3"

"repeat-string@^1.5.2", "repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"repeating@^2.0.0":
  "integrity" "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo="
  "resolved" "https://registry.npm.taobao.org/repeating/download/repeating-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-finite" "^1.0.0"

"request-progress@3.0.0":
  "integrity" "sha1-TKdUCBx/7GP1BeT6qCWqBs1mnb4="
  "resolved" "https://registry.npm.taobao.org/request-progress/download/request-progress-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "throttleit" "^1.0.0"

"request-promise-core@1.1.3":
  "integrity" "sha1-6aPAgbUTgN/qZ3M2Bh/qh5qCnuk="
  "resolved" "https://registry.npm.taobao.org/request-promise-core/download/request-promise-core-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "lodash" "^4.17.15"

"request-promise-native@^1.0.5", "request-promise-native@^1.0.7":
  "integrity" "sha1-pFW5YLgm5E4r+Jma9k3/K/5YyzY="
  "resolved" "https://registry.npm.taobao.org/request-promise-native/download/request-promise-native-1.0.8.tgz?cache=0&sync_timestamp=1572829773221&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frequest-promise-native%2Fdownload%2Frequest-promise-native-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "request-promise-core" "1.1.3"
    "stealthy-require" "^1.1.1"
    "tough-cookie" "^2.3.3"

"request@^2.34", "request@^2.87.0", "request@2.88.0":
  "integrity" "sha1-nC/KT301tZLv5Xx/ClXoEFIST+8="
  "resolved" "https://registry.npm.taobao.org/request/download/request-2.88.0.tgz"
  "version" "2.88.0"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.0"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.4.3"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@^1.0.1":
  "integrity" "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="
  "resolved" "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz"
  "version" "1.0.1"

"require-main-filename@^2.0.0":
  "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
  "resolved" "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"require-uncached@^1.0.3":
  "integrity" "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM="
  "resolved" "https://registry.npm.taobao.org/require-uncached/download/require-uncached-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "caller-path" "^0.1.0"
    "resolve-from" "^1.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"reselect@^3.0.1":
  "integrity" "sha1-79qpjqdFEyTQkrKyFjpqHXqaIUc="
  "resolved" "https://registry.npm.taobao.org/reselect/download/reselect-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freselect%2Fdownload%2Freselect-3.0.1.tgz"
  "version" "3.0.1"

"resize-observer-polyfill@^1.5.0":
  "integrity" "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="
  "resolved" "https://registry.npm.taobao.org/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@^2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://registry.npm.taobao.org/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-from@^1.0.0":
  "integrity" "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-1.0.1.tgz"
  "version" "1.0.1"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.11.0", "resolve@^1.12.0", "resolve@^1.3.2", "resolve@^1.4.0", "resolve@^1.5.0", "resolve@^1.8.1", "resolve@1.x":
  "integrity" "sha1-vgqkwGrNUwg1BauzX01mkyqzXRY="
  "resolved" "https://registry.npm.taobao.org/resolve/download/resolve-1.13.1.tgz"
  "version" "1.13.1"
  dependencies:
    "path-parse" "^1.0.6"

"resolve@1.1.7":
  "integrity" "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs="
  "resolved" "https://registry.npm.taobao.org/resolve/download/resolve-1.1.7.tgz"
  "version" "1.1.7"

"restore-cursor@^1.0.1":
  "integrity" "sha1-NGYfRohjJ/7SmRR5FSJS35LapUE="
  "resolved" "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "exit-hook" "^1.0.0"
    "onetime" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34="
  "resolved" "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "https://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="
  "resolved" "https://registry.npm.taobao.org/retry/download/retry-0.12.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fretry%2Fdownload%2Fretry-0.12.0.tgz"
  "version" "0.12.0"

"rgb-regex@^1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://registry.npm.taobao.org/rgb-regex/download/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://registry.npm.taobao.org/rgba-regex/download/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rimraf@^2.5.2", "rimraf@^2.5.4", "rimraf@^2.6.1", "rimraf@^2.6.2", "rimraf@^2.6.3":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@~2.6.2":
  "integrity" "sha1-stEE/g2Psnz54KHNqCYt04M8bKs="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "^7.1.3"

"rimraf@2.6.3":
  "integrity" "sha1-stEE/g2Psnz54KHNqCYt04M8bKs="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="
  "resolved" "https://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"rsvp@^3.3.3":
  "integrity" "sha1-LpZJFZmpbN4bUV1WdKj3qRRSkmo="
  "resolved" "https://registry.npm.taobao.org/rsvp/download/rsvp-3.6.2.tgz"
  "version" "3.6.2"

"rsvp@^4.8.4":
  "integrity" "sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ="
  "resolved" "https://registry.npm.taobao.org/rsvp/download/rsvp-4.8.5.tgz"
  "version" "4.8.5"

"run-async@^2.2.0":
  "integrity" "sha1-A3GrSuC91yDUFm19/aZP96RFpsA="
  "resolved" "https://registry.npm.taobao.org/run-async/download/run-async-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "is-promise" "^2.1.0"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rx-lite-aggregates@^4.0.8":
  "integrity" "sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74="
  "resolved" "https://registry.npm.taobao.org/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "rx-lite" "*"

"rx-lite@*", "rx-lite@^4.0.8":
  "integrity" "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ="
  "resolved" "https://registry.npm.taobao.org/rx-lite/download/rx-lite-4.0.8.tgz"
  "version" "4.0.8"

"rxjs@^5.0.0-beta.11":
  "integrity" "sha1-b6YbinfD15PbrycL7i9D9lLXQcw="
  "resolved" "https://registry.npm.taobao.org/rxjs/download/rxjs-5.5.12.tgz"
  "version" "5.5.12"
  dependencies:
    "symbol-observable" "1.0.1"

"rxjs@^6.4.0":
  "integrity" "sha1-UQ4mMX9NuRp+sd532d2boKSJmjo="
  "resolved" "https://registry.npm.taobao.org/rxjs/download/rxjs-6.5.3.tgz"
  "version" "6.5.3"
  dependencies:
    "tslib" "^1.9.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz?cache=0&sync_timestamp=1562349888578&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafe-buffer%2Fdownload%2Fsafe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sane@^2.0.0":
  "integrity" "sha1-tNwYYcIbQn6SlQej51HiosuKs/o="
  "resolved" "https://registry.npm.taobao.org/sane/download/sane-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "anymatch" "^2.0.0"
    "capture-exit" "^1.2.0"
    "exec-sh" "^0.2.0"
    "fb-watchman" "^2.0.0"
    "micromatch" "^3.1.4"
    "minimist" "^1.1.1"
    "walker" "~1.0.5"
    "watch" "~0.18.0"
  optionalDependencies:
    "fsevents" "^1.2.3"

"sane@^4.0.3":
  "integrity" "sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0="
  "resolved" "https://registry.npm.taobao.org/sane/download/sane-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    "anymatch" "^2.0.0"
    "capture-exit" "^2.0.0"
    "exec-sh" "^0.3.2"
    "execa" "^1.0.0"
    "fb-watchman" "^2.0.0"
    "micromatch" "^3.1.4"
    "minimist" "^1.1.1"
    "walker" "~1.0.5"

"sass-loader@^7.3.1":
  "integrity" "sha1-pb9ooEvOocE/+ELXRxUPerfQ0j8="
  "resolved" "https://registry.npm.taobao.org/sass-loader/download/sass-loader-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "clone-deep" "^4.0.1"
    "loader-utils" "^1.0.1"
    "neo-async" "^2.5.0"
    "pify" "^4.0.1"
    "semver" "^6.3.0"

"sass@^1.22.10":
  "integrity" "sha1-CQJU4AavEhnUQvG/8x4TnV4IXf8="
  "resolved" "https://registry.npm.taobao.org/sass/download/sass-1.23.7.tgz"
  "version" "1.23.7"
  dependencies:
    "chokidar" ">=2.0.0 <4.0.0"

"sax@^1.2.4", "sax@~1.2.4":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "https://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^1.0.0":
  "integrity" "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="
  "resolved" "https://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz?cache=0&sync_timestamp=1574946791935&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^2.5.0":
  "integrity" "sha1-63jwuUXHvPoggrNWXo2zVIAR3E8="
  "resolved" "https://registry.npm.taobao.org/schema-utils/download/schema-utils-2.6.1.tgz?cache=0&sync_timestamp=1574946791935&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npm.taobao.org/select-hose/download/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@^1.10.7":
  "integrity" "sha1-2lgZ/QSdVXTyjoipvMbbxubzkGs="
  "resolved" "https://registry.npm.taobao.org/selfsigned/download/selfsigned-1.10.7.tgz"
  "version" "1.10.7"
  dependencies:
    "node-forge" "0.9.0"

"semver@^5.0.1", "semver@^5.3.0", "semver@^5.4.1", "semver@^5.5", "semver@^5.5.0", "semver@^5.5.1", "semver@^5.6.0", "semver@2 || 3 || 4 || 5":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1565627380363&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.2.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1565627380363&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.2":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1565627380363&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1565627380363&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  "version" "6.3.0"

"semver@5.5.0":
  "integrity" "sha1-3Eu8emyp2Rbe5dQ1FvAJK1j3uKs="
  "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.5.0.tgz?cache=0&sync_timestamp=1565627380363&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.5.0.tgz"
  "version" "5.5.0"

"send@0.17.1":
  "integrity" "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg="
  "resolved" "https://registry.npm.taobao.org/send/download/send-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-javascript@^1.4.0", "serialize-javascript@^1.7.0":
  "integrity" "sha1-z8IArvd7YAxH2pu4FJyUPnmML9s="
  "resolved" "https://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-1.9.1.tgz"
  "version" "1.9.1"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npm.taobao.org/serve-index/download/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.1":
  "integrity" "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk="
  "resolved" "https://registry.npm.taobao.org/serve-static/download/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.1"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://registry.npm.taobao.org/set-value/download/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsetprototypeof%2Fdownload%2Fsetprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.1.1":
  "integrity" "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="
  "resolved" "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsetprototypeof%2Fdownload%2Fsetprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="
  "resolved" "https://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsha.js%2Fdownload%2Fsha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shallow-clone@^3.0.0":
  "integrity" "sha1-jymBrZJTH1UDWwH7IwdppA4C76M="
  "resolved" "https://registry.npm.taobao.org/shallow-clone/download/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "https://registry.npm.taobao.org/shebang-command/download/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.6.1":
  "integrity" "sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I="
  "resolved" "https://registry.npm.taobao.org/shell-quote/download/shell-quote-1.7.2.tgz"
  "version" "1.7.2"

"shellwords@^0.1.1":
  "integrity" "sha1-1rkYHBpI05cyTISHHvvPxz/AZUs="
  "resolved" "https://registry.npm.taobao.org/shellwords/download/shellwords-0.1.1.tgz"
  "version" "0.1.1"

"shvl@^2.0.0":
  "integrity" "sha1-Vf1VC26Bv3V08vV2uLXB/6504Q8="
  "resolved" "https://registry.npm.taobao.org/shvl/download/shvl-2.0.0.tgz"
  "version" "2.0.0"

"sigmund@^1.0.1":
  "integrity" "sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA="
  "resolved" "https://registry.npm.taobao.org/sigmund/download/sigmund-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsigmund%2Fdownload%2Fsigmund-1.0.1.tgz"
  "version" "1.0.1"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="
  "resolved" "https://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz"
  "version" "3.0.2"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://registry.npm.taobao.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"sisteransi@^0.1.1":
  "integrity" "sha1-VDFEfV99FnWqxmfM0LhlpJlMs84="
  "resolved" "https://registry.npm.taobao.org/sisteransi/download/sisteransi-0.1.1.tgz?cache=0&sync_timestamp=1573411519991&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsisteransi%2Fdownload%2Fsisteransi-0.1.1.tgz"
  "version" "0.1.1"

"sisteransi@^1.0.3":
  "integrity" "sha1-OGcT8e9ojHwDBNxMBjKJiUHK0uM="
  "resolved" "https://registry.npm.taobao.org/sisteransi/download/sisteransi-1.0.4.tgz?cache=0&sync_timestamp=1573411519991&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsisteransi%2Fdownload%2Fsisteransi-1.0.4.tgz"
  "version" "1.0.4"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz"
  "version" "1.0.0"

"slash@^2.0.0":
  "integrity" "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q="
  "resolved" "https://registry.npm.taobao.org/slash/download/slash-2.0.0.tgz"
  "version" "2.0.0"

"slice-ansi@^2.1.0":
  "integrity" "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY="
  "resolved" "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "astral-regex" "^1.0.0"
    "is-fullwidth-code-point" "^2.0.0"

"slice-ansi@0.0.4":
  "integrity" "sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU="
  "resolved" "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-0.0.4.tgz"
  "version" "0.0.4"

"slice-ansi@1.0.0":
  "integrity" "sha1-BE8aSdiEL/MHqta1Be0Xi9lQE00="
  "resolved" "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "https://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "https://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sockjs-client@1.4.0":
  "integrity" "sha1-yfJWjhnI/YFztJl+o0IOC7MGx9U="
  "resolved" "https://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsockjs-client%2Fdownload%2Fsockjs-client-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "debug" "^3.2.5"
    "eventsource" "^1.0.7"
    "faye-websocket" "~0.11.1"
    "inherits" "^2.0.3"
    "json3" "^3.3.2"
    "url-parse" "^1.4.3"

"sockjs@0.3.19":
  "integrity" "sha1-2Xa76ACve9IK4IWY1YI5NQiZPA0="
  "resolved" "https://registry.npm.taobao.org/sockjs/download/sockjs-0.3.19.tgz"
  "version" "0.3.19"
  dependencies:
    "faye-websocket" "^0.10.0"
    "uuid" "^3.0.1"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://registry.npm.taobao.org/sort-keys/download/sort-keys-1.1.2.tgz?cache=0&sync_timestamp=1565864727994&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsort-keys%2Fdownload%2Fsort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="
  "resolved" "https://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-resolve@^0.5.0", "source-map-resolve@^0.5.2":
  "integrity" "sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk="
  "resolved" "https://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "atob" "^2.1.1"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.4.15":
  "integrity" "sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8="
  "resolved" "https://registry.npm.taobao.org/source-map-support/download/source-map-support-0.4.18.tgz?cache=0&sync_timestamp=1572390697943&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.4.18.tgz"
  "version" "0.4.18"
  dependencies:
    "source-map" "^0.5.6"

"source-map-support@^0.5.6", "source-map-support@~0.5.12":
  "integrity" "sha1-CuBp5/47p1OMZMmFFeNTOerFoEI="
  "resolved" "https://registry.npm.taobao.org/source-map-support/download/source-map-support-0.5.16.tgz?cache=0&sync_timestamp=1572390697943&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.16.tgz"
  "version" "0.5.16"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="
  "resolved" "https://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.0.tgz"
  "version" "0.4.0"

"source-map@^0.5.0":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.3":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.6", "source-map@^0.5.7":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0", "source-map@^0.6.1", "source-map@~0.6.0", "source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz"
  "version" "0.7.3"

"spdx-correct@^3.0.0":
  "integrity" "sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ="
  "resolved" "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc="
  "resolved" "https://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz"
  "version" "2.2.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-meEZt6XaAOBUkcn6M4t5BII7QdA="
  "resolved" "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ="
  "resolved" "https://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz"
  "version" "3.0.5"

"spdy-transport@^3.0.0":
  "integrity" "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE="
  "resolved" "https://registry.npm.taobao.org/spdy-transport/download/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.1":
  "integrity" "sha1-bxLtHF236k8k67i4m6WMh8CCV/I="
  "resolved" "https://registry.npm.taobao.org/spdy/download/spdy-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc="
  "resolved" "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz"
  "version" "1.16.1"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssri@^5.2.4":
  "integrity" "sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY="
  "resolved" "https://registry.npm.taobao.org/ssri/download/ssri-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "safe-buffer" "^5.1.1"

"ssri@^6.0.1":
  "integrity" "sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg="
  "resolved" "https://registry.npm.taobao.org/ssri/download/ssri-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "figgy-pudding" "^3.5.1"

"stable@^0.1.8":
  "integrity" "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88="
  "resolved" "https://registry.npm.taobao.org/stable/download/stable-0.1.8.tgz"
  "version" "0.1.8"

"stack-utils@^1.0.1":
  "integrity" "sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g="
  "resolved" "https://registry.npm.taobao.org/stack-utils/download/stack-utils-1.0.2.tgz"
  "version" "1.0.2"

"stackframe@^1.1.0":
  "integrity" "sha1-4/wuuRIllHnJgi99Hx/zZb1cvIM="
  "resolved" "https://registry.npm.taobao.org/stackframe/download/stackframe-1.1.0.tgz"
  "version" "1.1.0"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz"
  "version" "1.5.0"

"stealthy-require@^1.1.1":
  "integrity" "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks="
  "resolved" "https://registry.npm.taobao.org/stealthy-require/download/stealthy-require-1.1.1.tgz"
  "version" "1.1.1"

"stream-browserify@^2.0.1":
  "integrity" "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs="
  "resolved" "https://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="
  "resolved" "https://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="
  "resolved" "https://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI="
  "resolved" "https://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.0.tgz"
  "version" "1.0.0"

"stream-to-observable@^0.1.0":
  "integrity" "sha1-Rb8dny19wJvtgfHDB8Qw5ouEz/4="
  "resolved" "https://registry.npm.taobao.org/stream-to-observable/download/stream-to-observable-0.1.0.tgz"
  "version" "0.1.0"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-length@^2.0.0":
  "integrity" "sha1-1A27aGo6zpYMHP/KVivyxF+DY+0="
  "resolved" "https://registry.npm.taobao.org/string-length/download/string-length-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "astral-regex" "^1.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^1.0.1":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "https://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-width%2Fdownload%2Fstring-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^2.0.0", "string-width@^2.1.0", "string-width@^2.1.1":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^3.0.0", "string-width@^3.1.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "https://registry.npm.taobao.org/string-width/download/string-width-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-width%2Fdownload%2Fstring-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha1-lSGCxGzHssMT0VluYjmSvRY7crU="
  "resolved" "https://registry.npm.taobao.org/string-width/download/string-width-4.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-width%2Fdownload%2Fstring-width-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.0"

"string.prototype.padend@^3.0.0":
  "integrity" "sha1-86rvfBcZ8XDF6rHDK/eA2W4h8vA="
  "resolved" "https://registry.npm.taobao.org/string.prototype.padend/download/string.prototype.padend-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "es-abstract" "^1.4.3"
    "function-bind" "^1.0.2"

"string.prototype.padstart@^3.0.0":
  "integrity" "sha1-W8+tOfRkm7LQMSkuGbzwtRDUskI="
  "resolved" "https://registry.npm.taobao.org/string.prototype.padstart/download/string.prototype.padstart-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "es-abstract" "^1.4.3"
    "function-bind" "^1.0.2"

"string.prototype.trimleft@^2.1.0":
  "integrity" "sha1-bMR/DX641isPNwFhFxWjlUWR1jQ="
  "resolved" "https://registry.npm.taobao.org/string.prototype.trimleft/download/string.prototype.trimleft-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "define-properties" "^1.1.3"
    "function-bind" "^1.1.1"

"string.prototype.trimright@^2.1.0":
  "integrity" "sha1-Zp0WS+nfm291WfqOiZRbFopabFg="
  "resolved" "https://registry.npm.taobao.org/string.prototype.trimright/download/string.prototype.trimright-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "define-properties" "^1.1.3"
    "function-bind" "^1.1.1"

"stringify-object@^3.2.2":
  "integrity" "sha1-cDBlrvyhkwDTzoivT1s5VtdVZik="
  "resolved" "https://registry.npm.taobao.org/stringify-object/download/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "^3.0.0"
    "is-obj" "^1.0.1"
    "is-regexp" "^1.0.0"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&sync_timestamp=1573280549549&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz?cache=0&sync_timestamp=1573280549549&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^5.0.0", "strip-ansi@^5.1.0", "strip-ansi@^5.2.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1573280549549&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0":
  "integrity" "sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI="
  "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&sync_timestamp=1573280549549&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "ansi-regex" "^5.0.0"

"strip-bom@^2.0.0":
  "integrity" "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4="
  "resolved" "https://registry.npm.taobao.org/strip-bom/download/strip-bom-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-utf8" "^0.2.0"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.npm.taobao.org/strip-bom/download/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-bom@3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.npm.taobao.org/strip-bom/download/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-comments@^1.0.2":
  "integrity" "sha1-grnEXn8FhzvuU/NxaK+TCqNoZ50="
  "resolved" "https://registry.npm.taobao.org/strip-comments/download/strip-comments-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "babel-extract-comments" "^1.0.0"
    "babel-plugin-transform-object-rest-spread" "^6.26.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="
  "resolved" "https://registry.npm.taobao.org/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g="
  "resolved" "https://registry.npm.taobao.org/strip-indent/download/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^2.0.0", "strip-json-comments@~2.0.1":
  "integrity" "sha1-PFMZQukIwml8DsNEhYwobHygpgo="
  "resolved" "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"strip-json-comments@^3.0.1":
  "integrity" "sha1-hXE5dakfuHvxswXMp3OV5A0qZKc="
  "resolved" "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-3.0.1.tgz"
  "version" "3.0.1"

"style-resources-loader@^1.2.1":
  "integrity" "sha1-3uA04z6EBg3tCxmA+GZFPphYmHM="
  "resolved" "https://registry.npm.taobao.org/style-resources-loader/download/style-resources-loader-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "glob" "^7.1.6"
    "is-promise" "^2.1.0"
    "loader-utils" "^1.2.3"
    "schema-utils" "^2.5.0"

"stylehacks@^4.0.0":
  "integrity" "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU="
  "resolved" "https://registry.npm.taobao.org/stylehacks/download/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.1.2":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^5.3.0", "supports-color@^5.4.0", "supports-color@5.5.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E="
  "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "has-flag" "^4.0.0"

"svg-tags@^1.0.0":
  "integrity" "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q="
  "resolved" "https://registry.npm.taobao.org/svg-tags/download/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^1.0.0", "svgo@^1.0.5":
  "integrity" "sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc="
  "resolved" "https://registry.npm.taobao.org/svgo/download/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"symbol-observable@1.0.1":
  "integrity" "sha1-g0D8RwLDEi310iKI+IKD9RPT/dQ="
  "resolved" "https://registry.npm.taobao.org/symbol-observable/download/symbol-observable-1.0.1.tgz"
  "version" "1.0.1"

"symbol-tree@^3.2.2":
  "integrity" "sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I="
  "resolved" "https://registry.npm.taobao.org/symbol-tree/download/symbol-tree-3.2.4.tgz?cache=0&sync_timestamp=1560363067941&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsymbol-tree%2Fdownload%2Fsymbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"table@^5.2.3":
  "integrity" "sha1-EpLRlQDOP4YFOwXw6Ofko7shB54="
  "resolved" "https://registry.npm.taobao.org/table/download/table-5.4.6.tgz"
  "version" "5.4.6"
  dependencies:
    "ajv" "^6.10.2"
    "lodash" "^4.17.14"
    "slice-ansi" "^2.1.0"
    "string-width" "^3.0.0"

"table@4.0.2":
  "integrity" "sha1-ozRHN1OR52atNNNIbm4q7chNLjY="
  "resolved" "https://registry.npm.taobao.org/table/download/table-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "ajv" "^5.2.3"
    "ajv-keywords" "^2.1.0"
    "chalk" "^2.1.0"
    "lodash" "^4.17.4"
    "slice-ansi" "1.0.0"
    "string-width" "^2.1.1"

"tapable@^1.0.0", "tapable@^1.1.3":
  "integrity" "sha1-ofzMBrWNth/XpF2i2kT186Pme6I="
  "resolved" "https://registry.npm.taobao.org/tapable/download/tapable-1.1.3.tgz"
  "version" "1.1.3"

"terser-webpack-plugin@^1.2.3", "terser-webpack-plugin@^1.4.1":
  "integrity" "sha1-YbGOQOruW+l+dxzbsQ7RKAiIwrQ="
  "resolved" "https://registry.npm.taobao.org/terser-webpack-plugin/download/terser-webpack-plugin-1.4.1.tgz?cache=0&sync_timestamp=1571752410401&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "cacache" "^12.0.2"
    "find-cache-dir" "^2.1.0"
    "is-wsl" "^1.1.0"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^1.7.0"
    "source-map" "^0.6.1"
    "terser" "^4.1.2"
    "webpack-sources" "^1.4.0"
    "worker-farm" "^1.7.0"

"terser@^4.1.2":
  "integrity" "sha1-RI//rQJF9Miid86JeItFi/13Bug="
  "resolved" "https://registry.npm.taobao.org/terser/download/terser-4.4.2.tgz"
  "version" "4.4.2"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.6.1"
    "source-map-support" "~0.5.12"

"test-exclude@^4.2.1":
  "integrity" "sha1-qaXmRHTkOYM5JFoKdprXwvSpfCA="
  "resolved" "https://registry.npm.taobao.org/test-exclude/download/test-exclude-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "arrify" "^1.0.1"
    "micromatch" "^2.3.11"
    "object-assign" "^4.1.0"
    "read-pkg-up" "^1.0.1"
    "require-main-filename" "^1.0.1"

"test-exclude@^5.2.3":
  "integrity" "sha1-w9Ph4xHrfuQF4JLawQrv0JCR6sA="
  "resolved" "https://registry.npm.taobao.org/test-exclude/download/test-exclude-5.2.3.tgz"
  "version" "5.2.3"
  dependencies:
    "glob" "^7.1.3"
    "minimatch" "^3.0.4"
    "read-pkg-up" "^4.0.0"
    "require-main-filename" "^2.0.0"

"text-table@^0.2.0", "text-table@~0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npm.taobao.org/text-table/download/text-table-0.2.0.tgz"
  "version" "0.2.0"

"thenify-all@^1.0.0":
  "integrity" "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY="
  "resolved" "https://registry.npm.taobao.org/thenify-all/download/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha1-5p44obq+lpsBCCB5eLn2K4hgSDk="
  "resolved" "https://registry.npm.taobao.org/thenify/download/thenify-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^2.1.2":
  "integrity" "sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo="
  "resolved" "https://registry.npm.taobao.org/thread-loader/download/thread-loader-2.1.3.tgz?cache=0&sync_timestamp=1565261083321&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthread-loader%2Fdownload%2Fthread-loader-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "loader-runner" "^2.3.1"
    "loader-utils" "^1.1.0"
    "neo-async" "^2.6.0"

"throat@^4.0.0":
  "integrity" "sha1-iQN8vJLFarGJJua6TLsgDhVnKmo="
  "resolved" "https://registry.npm.taobao.org/throat/download/throat-4.1.0.tgz"
  "version" "4.1.0"

"throttle-debounce@^1.0.1":
  "integrity" "sha1-UYU9o3vmihVctugns1FKPEIuic0="
  "resolved" "https://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-1.1.0.tgz"
  "version" "1.1.0"

"throttleit@^1.0.0":
  "integrity" "sha1-nnhYNtr0Z0MUWlmEtiaNgoUorGw="
  "resolved" "https://registry.npm.taobao.org/throttleit/download/throttleit-1.0.0.tgz"
  "version" "1.0.0"

"through@^2.3.6":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "https://registry.npm.taobao.org/through2/download/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="
  "resolved" "https://registry.npm.taobao.org/thunky/download/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timers-browserify@^2.0.4":
  "integrity" "sha1-gAsfPu4nLlvFPuRloE0OgEwxIR8="
  "resolved" "https://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.11.tgz?cache=0&sync_timestamp=1565448362964&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftimers-browserify%2Fdownload%2Ftimers-browserify-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "setimmediate" "^1.0.4"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npm.taobao.org/timsort/download/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tmp@^0.0.33":
  "integrity" "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk="
  "resolved" "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"tmp@0.1.0":
  "integrity" "sha1-7kNKTiJUMILilLpiAdzG6v76KHc="
  "resolved" "https://registry.npm.taobao.org/tmp/download/tmp-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "rimraf" "^2.6.3"

"tmpl@1.0.x":
  "integrity" "sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE="
  "resolved" "https://registry.npm.taobao.org/tmpl/download/tmpl-1.0.4.tgz"
  "version" "1.0.4"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-1.0.3.tgz?cache=0&sync_timestamp=1573620967166&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fto-fast-properties%2Fdownload%2Fto-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz?cache=0&sync_timestamp=1573620967166&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fto-fast-properties%2Fdownload%2Fto-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "https://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.0":
  "integrity" "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="
  "resolved" "https://registry.npm.taobao.org/toidentifier/download/toidentifier-1.0.0.tgz"
  "version" "1.0.0"

"topo@2.x.x":
  "integrity" "sha1-zVYVdSU5BXwNwEkaYhw7xvvh0YI="
  "resolved" "https://registry.npm.taobao.org/topo/download/topo-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hoek" "4.x.x"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@^2.3.3", "tough-cookie@^2.3.4", "tough-cookie@~2.4.3":
  "integrity" "sha1-U/Nto/R3g7CSWvoG/587FlKA94E="
  "resolved" "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "psl" "^1.1.24"
    "punycode" "^1.4.1"

"tr46@^1.0.1":
  "integrity" "sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk="
  "resolved" "https://registry.npm.taobao.org/tr46/download/tr46-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "punycode" "^2.1.0"

"trim-right@^1.0.1":
  "integrity" "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM="
  "resolved" "https://registry.npm.taobao.org/trim-right/download/trim-right-1.0.1.tgz"
  "version" "1.0.1"

"tryer@^1.0.1":
  "integrity" "sha1-8shUBoALmw90yfdGW4HqrSQSUvg="
  "resolved" "https://registry.npm.taobao.org/tryer/download/tryer-1.0.1.tgz"
  "version" "1.0.1"

"ts-jest@^24.0.2":
  "integrity" "sha1-eryijCtLCh/dcVzWZ9ZdBH6k52g="
  "resolved" "https://registry.npm.taobao.org/ts-jest/download/ts-jest-24.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fts-jest%2Fdownload%2Fts-jest-24.2.0.tgz"
  "version" "24.2.0"
  dependencies:
    "bs-logger" "0.x"
    "buffer-from" "1.x"
    "fast-json-stable-stringify" "2.x"
    "json5" "2.x"
    "lodash.memoize" "4.x"
    "make-error" "1.x"
    "mkdirp" "0.x"
    "resolve" "1.x"
    "semver" "^5.5"
    "yargs-parser" "10.x"

"ts-loader@^5.3.3":
  "integrity" "sha1-oMHwNLAXqTRM7wlhv9l8wZJJK4s="
  "resolved" "https://registry.npm.taobao.org/ts-loader/download/ts-loader-5.4.5.tgz"
  "version" "5.4.5"
  dependencies:
    "chalk" "^2.3.0"
    "enhanced-resolve" "^4.0.0"
    "loader-utils" "^1.0.2"
    "micromatch" "^3.1.4"
    "semver" "^5.0.1"

"tsconfig@^7.0.0":
  "integrity" "sha1-hFOIdaTcIW5cSlQys6Tew9VOkbc="
  "resolved" "https://registry.npm.taobao.org/tsconfig/download/tsconfig-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "@types/strip-bom" "^3.0.0"
    "@types/strip-json-comments" "0.0.30"
    "strip-bom" "^3.0.0"
    "strip-json-comments" "^2.0.0"

"tslib@^1.8.0", "tslib@^1.8.1", "tslib@^1.9.0", "tslib@^1.9.3":
  "integrity" "sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo="
  "resolved" "https://registry.npm.taobao.org/tslib/download/tslib-1.10.0.tgz"
  "version" "1.10.0"

"tslib@2.3.0":
  "integrity" "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.3.0.tgz"
  "version" "2.3.0"

"tslint@^4.0.0 || ^5.0.0", "tslint@^5.15.0":
  "integrity" "sha1-5AHortoBUrxE3QfmFANPP4DGe30="
  "resolved" "https://registry.npm.taobao.org/tslint/download/tslint-5.20.1.tgz"
  "version" "5.20.1"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "builtin-modules" "^1.1.1"
    "chalk" "^2.3.0"
    "commander" "^2.12.1"
    "diff" "^4.0.1"
    "glob" "^7.1.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.1"
    "resolve" "^1.3.2"
    "semver" "^5.3.0"
    "tslib" "^1.8.0"
    "tsutils" "^2.29.0"

"tsutils@^2.29.0":
  "integrity" "sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k="
  "resolved" "https://registry.npm.taobao.org/tsutils/download/tsutils-2.29.0.tgz"
  "version" "2.29.0"
  dependencies:
    "tslib" "^1.8.1"

"tsutils@^3.7.0":
  "integrity" "sha1-7XGZF/EcoN7lhicrKsSeAVot11k="
  "resolved" "https://registry.npm.taobao.org/tsutils/download/tsutils-3.17.1.tgz"
  "version" "3.17.1"
  dependencies:
    "tslib" "^1.8.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://registry.npm.taobao.org/type-check/download/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-fest@^0.6.0":
  "integrity" "sha1-jSojcNPfiG61yQraHFv2GIrPg4s="
  "resolved" "https://registry.npm.taobao.org/type-fest/download/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0="
  "resolved" "https://registry.npm.taobao.org/type-fest/download/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type-is@~1.6.17", "type-is@~1.6.18":
  "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
  "resolved" "https://registry.npm.taobao.org/type-is/download/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"typescript@*", "typescript@^2.1.0 || ^3.0.0", "typescript@>=2", "typescript@>=2.1.0 || >=2.1.0-dev || >=2.2.0-dev || >=2.3.0-dev || >=2.4.0-dev || >=2.5.0-dev || >=2.6.0-dev || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev || >= 3.0.0-dev || >= 3.1.0-dev", "typescript@>=2.3.0-dev || >=2.4.0-dev || >=2.5.0-dev || >=2.6.0-dev || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev || >=3.0.0-dev || >= 3.1.0-dev || >= 3.2.0-dev", "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta", "typescript@3.6.2":
  "integrity" "sha1-EFsPGTQRnd5UOsjrca86kQCe/lQ="
  "resolved" "https://registry.npm.taobao.org/typescript/download/typescript-3.6.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftypescript%2Fdownload%2Ftypescript-3.6.2.tgz"
  "version" "3.6.2"

"uglify-js@^3.1.4":
  "integrity" "sha1-NcfeF5caSqdonNLq4KWzm7g4wMU="
  "resolved" "https://registry.npm.taobao.org/uglify-js/download/uglify-js-3.7.1.tgz?cache=0&sync_timestamp=1575088176536&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuglify-js%2Fdownload%2Fuglify-js-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "commander" "~2.20.3"
    "source-map" "~0.6.1"

"uglify-js@3.4.x":
  "integrity" "sha1-mtlWPY6zrN+404WX0q8dgV9qdV8="
  "resolved" "https://registry.npm.taobao.org/uglify-js/download/uglify-js-3.4.10.tgz?cache=0&sync_timestamp=1575088176536&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuglify-js%2Fdownload%2Fuglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"underscore-plus@1.x":
  "integrity" "sha1-EH8ZAMUgrB/v5O3sZYCn/wipnQ8="
  "resolved" "https://registry.npm.taobao.org/underscore-plus/download/underscore-plus-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "underscore" "^1.9.1"

"underscore@^1.9.1":
  "integrity" "sha1-BtzjSg5op7q8KbNluOdLiSUgOWE="
  "resolved" "https://registry.npm.taobao.org/underscore/download/underscore-1.9.1.tgz"
  "version" "1.9.1"

"unicode-canonical-property-names-ecmascript@^1.0.4":
  "integrity" "sha1-JhmADEyCWADv3YNDr33Zkzy+KBg="
  "resolved" "https://registry.npm.taobao.org/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  "version" "1.0.4"

"unicode-match-property-ecmascript@^1.0.4":
  "integrity" "sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw="
  "resolved" "https://registry.npm.taobao.org/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^1.0.4"
    "unicode-property-aliases-ecmascript" "^1.0.4"

"unicode-match-property-value-ecmascript@^1.1.0":
  "integrity" "sha1-W0tCbgjROoA2Xg1lesemwexGonc="
  "resolved" "https://registry.npm.taobao.org/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.1.0.tgz"
  "version" "1.1.0"

"unicode-property-aliases-ecmascript@^1.0.4":
  "integrity" "sha1-qcxsx85joKMCP8meNBuUQx1AWlc="
  "resolved" "https://registry.npm.taobao.org/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.0.5.tgz"
  "version" "1.0.5"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.0", "unique-filename@^1.1.1":
  "integrity" "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="
  "resolved" "https://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw="
  "resolved" "https://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"universalify@^0.1.0":
  "integrity" "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="
  "resolved" "https://registry.npm.taobao.org/universalify/download/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://registry.npm.taobao.org/unquote/download/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"untildify@3.0.3":
  "integrity" "sha1-HntCsUC8/ZIrIucMoSZb/jY0x8k="
  "resolved" "https://registry.npm.taobao.org/untildify/download/untildify-3.0.3.tgz"
  "version" "3.0.3"

"upath@^1.1.1":
  "integrity" "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ="
  "resolved" "https://registry.npm.taobao.org/upath/download/upath-1.2.0.tgz"
  "version" "1.2.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA="
  "resolved" "https://registry.npm.taobao.org/uri-js/download/uri-js-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^1.1.2":
  "integrity" "sha1-uXHRkbg69pPF4/6kBkvp4fLX+Ng="
  "resolved" "https://registry.npm.taobao.org/url-loader/download/url-loader-1.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-loader%2Fdownload%2Furl-loader-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "loader-utils" "^1.1.0"
    "mime" "^2.0.3"
    "schema-utils" "^1.0.0"

"url-parse@^1.4.3":
  "integrity" "sha1-qKg1NejACjFuQDpdtKwbm4U64ng="
  "resolved" "https://registry.npm.taobao.org/url-parse/download/url-parse-1.4.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-parse%2Fdownload%2Furl-parse-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url@^0.11.0", "url@0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.npm.taobao.org/url/download/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "https://registry.npm.taobao.org/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@^1.0.0", "util.promisify@~1.0.0", "util.promisify@1.0.0":
  "integrity" "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="
  "resolved" "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.11.0":
  "integrity" "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE="
  "resolved" "https://registry.npm.taobao.org/util/download/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://registry.npm.taobao.org/util/download/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@^0.4.0", "utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.0.1", "uuid@^3.3.2":
  "integrity" "sha1-RWjwIW54dg7h2/Ok0s9T4iQRKGY="
  "resolved" "https://registry.npm.taobao.org/uuid/download/uuid-3.3.3.tgz"
  "version" "3.3.3"

"v8-compile-cache@^2.0.3":
  "integrity" "sha1-4U3jezGm0ZT1aQ1n78Tn9vxqsw4="
  "resolved" "https://registry.npm.taobao.org/v8-compile-cache/download/v8-compile-cache-2.1.0.tgz"
  "version" "2.1.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "https://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha1-pkZ3gavTZiF8BQ+CAuflDMnu+MA="
  "resolved" "https://registry.npm.taobao.org/vendors/download/vendors-1.0.3.tgz"
  "version" "1.0.3"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fverror%2Fdownload%2Fverror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vm-browserify@^1.0.1":
  "integrity" "sha1-eGQcSIuObKkadfUR56OzKobl3aA="
  "resolved" "https://registry.npm.taobao.org/vm-browserify/download/vm-browserify-1.1.2.tgz?cache=0&sync_timestamp=1572870837170&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvm-browserify%2Fdownload%2Fvm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"vue-area-linkage@^5.1.0":
  "integrity" "sha512-86WD4O6PD9e2koWZ81Zlfcyey17U1+3B3fpWoyIZlBIe2V6qfvrg9BTozWdTpdF4CmKBiz138LBRTKhawl/UWw=="
  "resolved" "https://registry.npmjs.org/vue-area-linkage/-/vue-area-linkage-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lodash.find" "^4.6.0"

"vue-class-component@^6.0.0 || ^7.0.0", "vue-class-component@^7.1.0":
  "integrity" "sha1-sz78sQ4XI21oT3Cx6W8ZRux5Poc="
  "resolved" "https://registry.npm.taobao.org/vue-class-component/download/vue-class-component-7.1.0.tgz"
  "version" "7.1.0"

"vue-cli-plugin-element@^1.0.1":
  "integrity" "sha1-NOWPtls2z1mvrxT1AyiOXleLFVQ="
  "resolved" "https://registry.npm.taobao.org/vue-cli-plugin-element/download/vue-cli-plugin-element-1.0.1.tgz"
  "version" "1.0.1"

"vue-cli-plugin-style-resources-loader@^0.1.3":
  "integrity" "sha1-YIeoYTLqgSWqieX44Kl4+8jPb1k="
  "resolved" "https://registry.npm.taobao.org/vue-cli-plugin-style-resources-loader/download/vue-cli-plugin-style-resources-loader-0.1.4.tgz"
  "version" "0.1.4"

"vue-eslint-parser@^2.0.3":
  "integrity" "sha1-wmjJbG2Uz+PZOKX3WTlZsMozYNE="
  "resolved" "https://registry.npm.taobao.org/vue-eslint-parser/download/vue-eslint-parser-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "debug" "^3.1.0"
    "eslint-scope" "^3.7.1"
    "eslint-visitor-keys" "^1.0.0"
    "espree" "^3.5.2"
    "esquery" "^1.0.0"
    "lodash" "^4.17.4"

"vue-eslint-parser@^5.0.0":
  "integrity" "sha1-APTk2pTsl0uCGib/DtD3p4QCuKE="
  "resolved" "https://registry.npm.taobao.org/vue-eslint-parser/download/vue-eslint-parser-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "debug" "^4.1.0"
    "eslint-scope" "^4.0.0"
    "eslint-visitor-keys" "^1.0.0"
    "espree" "^4.1.0"
    "esquery" "^1.0.1"
    "lodash" "^4.17.11"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha1-UylVzB6yCKPZkLOp+acFdGV+CPI="
  "resolved" "https://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-jest@^3.0.4":
  "integrity" "sha1-1vEktULcv/IHv5KWwZQT9MQLcMk="
  "resolved" "https://registry.npm.taobao.org/vue-jest/download/vue-jest-3.0.5.tgz"
  "version" "3.0.5"
  dependencies:
    "babel-plugin-transform-es2015-modules-commonjs" "^6.26.0"
    "chalk" "^2.1.0"
    "extract-from-css" "^0.4.4"
    "find-babel-config" "^1.1.0"
    "js-beautify" "^1.6.14"
    "node-cache" "^4.1.1"
    "object-assign" "^4.1.1"
    "source-map" "^0.5.6"
    "tsconfig" "^7.0.0"
    "vue-template-es2015-compiler" "^1.6.0"

"vue-loader@^15.7.0":
  "integrity" "sha1-zInicW34f3D+ZWydqdf4vsBsc9Y="
  "resolved" "https://registry.npm.taobao.org/vue-loader/download/vue-loader-15.7.2.tgz"
  "version" "15.7.2"
  dependencies:
    "@vue/component-compiler-utils" "^3.0.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"vue-property-decorator@^8.2.2":
  "integrity" "sha1-U28CfcfWJvN8jYWi3ALwpsuXlEA="
  "resolved" "https://registry.npm.taobao.org/vue-property-decorator/download/vue-property-decorator-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "vue-class-component" "^7.1.0"

"vue-router@^3.1.2":
  "integrity" "sha1-5rFPq8DA7p/aDiy72nSzUOKOQSs="
  "resolved" "https://registry.npm.taobao.org/vue-router/download/vue-router-3.1.3.tgz?cache=0&sync_timestamp=1572571382028&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-router%2Fdownload%2Fvue-router-3.1.3.tgz"
  "version" "3.1.3"

"vue-style-loader@^4.1.0":
  "integrity" "sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg="
  "resolved" "https://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-svgicon@^3.2.6":
  "integrity" "sha1-tFm4AldBu2tNycVjLuTOPsY1C4k="
  "resolved" "https://registry.npm.taobao.org/vue-svgicon/download/vue-svgicon-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "camelcase" "^5.2.0"
    "colors" "^1.3.0"
    "fs-plus" "^3.0.2"
    "glob" "^7.1.2"
    "svgo" "^1.0.5"
    "tslib" "^1.9.3"
    "yargs" "^12.0.1"

"vue-template-compiler@^2.0.0", "vue-template-compiler@^2.5.16", "vue-template-compiler@^2.6.10", "vue-template-compiler@^2.x":
  "integrity" "sha1-MjtPNJXwT6o1AzN6gvXWUHeZycw="
  "resolved" "https://registry.npm.taobao.org/vue-template-compiler/download/vue-template-compiler-2.6.10.tgz"
  "version" "2.6.10"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"vue-template-es2015-compiler@^1.6.0", "vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU="
  "resolved" "https://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue@^2.0.0", "vue@^2.5.0", "vue@^2.5.17", "vue@^2.6.10", "vue@^2.x", "vue@>=2", "vue@>=2.2.0", "vue@2.x":
  "integrity" "sha1-pysaQqTYKnIepDjRtr9V5mGVxjc="
  "resolved" "https://registry.npm.taobao.org/vue/download/vue-2.6.10.tgz"
  "version" "2.6.10"

"vuex-class@^0.3.2":
  "integrity" "sha1-x+lqB2wWghN9TSOo3P3GPyIOF6g="
  "resolved" "https://registry.npm.taobao.org/vuex-class/download/vuex-class-0.3.2.tgz"
  "version" "0.3.2"

"vuex-module-decorators@^0.10.1":
  "integrity" "sha1-o06/fCrscB0ZrmDeoKjxYggl/Z4="
  "resolved" "https://registry.npm.taobao.org/vuex-module-decorators/download/vuex-module-decorators-0.10.1.tgz"
  "version" "0.10.1"

"vuex-persistedstate@^2.7.0":
  "integrity" "sha1-9gquThFjvyk2lqYlUm2/+qQuQp4="
  "resolved" "https://registry.npm.taobao.org/vuex-persistedstate/download/vuex-persistedstate-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "deepmerge" "^4.2.2"
    "shvl" "^2.0.0"

"vuex@^2.0.0 || ^3.0.0", "vuex@^3.0.0", "vuex@^3.1.1", "vuex@3":
  "integrity" "sha1-ooY/QAWqc/JYflXD+t8/AfacfU0="
  "resolved" "https://registry.npm.taobao.org/vuex/download/vuex-3.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvuex%2Fdownload%2Fvuex-3.1.2.tgz"
  "version" "3.1.2"

"w3c-hr-time@^1.0.1":
  "integrity" "sha1-gqwr/2PZUOqeMYmlimViX+3xkEU="
  "resolved" "https://registry.npm.taobao.org/w3c-hr-time/download/w3c-hr-time-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browser-process-hrtime" "^0.1.2"

"walker@^1.0.7", "walker@~1.0.5":
  "integrity" "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs="
  "resolved" "https://registry.npm.taobao.org/walker/download/walker-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "makeerror" "1.0.x"

"watch@~0.18.0":
  "integrity" "sha1-KAlUdsbffJDJYxOJkMClQj60uYY="
  "resolved" "https://registry.npm.taobao.org/watch/download/watch-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "exec-sh" "^0.2.0"
    "minimist" "^1.2.0"

"watchpack@^1.6.0":
  "integrity" "sha1-S8EsLr6KonenHx0/FNaFx7RGzQA="
  "resolved" "https://registry.npm.taobao.org/watchpack/download/watchpack-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "chokidar" "^2.0.2"
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98="
  "resolved" "https://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "https://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^4.0.2":
  "integrity" "sha1-qFWYCx8LazWbodXZ+zmulB+qY60="
  "resolved" "https://registry.npm.taobao.org/webidl-conversions/download/webidl-conversions-4.0.2.tgz"
  "version" "4.0.2"

"webpack-bundle-analyzer@^3.3.0":
  "integrity" "sha1-ObOo+CnKBEaCvG+eARyV3rVUrv0="
  "resolved" "https://registry.npm.taobao.org/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "acorn" "^6.0.7"
    "acorn-walk" "^6.1.1"
    "bfj" "^6.1.1"
    "chalk" "^2.4.1"
    "commander" "^2.18.0"
    "ejs" "^2.6.1"
    "express" "^4.16.3"
    "filesize" "^3.6.1"
    "gzip-size" "^5.0.0"
    "lodash" "^4.17.15"
    "mkdirp" "^0.5.1"
    "opener" "^1.5.1"
    "ws" "^6.0.0"

"webpack-chain@^4.11.0":
  "integrity" "sha1-bIQ5u7KrVQlS1g4eqTGRQZBsAqY="
  "resolved" "https://registry.npm.taobao.org/webpack-chain/download/webpack-chain-4.12.1.tgz"
  "version" "4.12.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^1.6.0"

"webpack-dev-middleware@^3.7.2":
  "integrity" "sha1-ABnD23FuP6XOy/ZPKriKdLqzMfM="
  "resolved" "https://registry.npm.taobao.org/webpack-dev-middleware/download/webpack-dev-middleware-3.7.2.tgz?cache=0&sync_timestamp=1572571387368&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-dev-middleware%2Fdownload%2Fwebpack-dev-middleware-3.7.2.tgz"
  "version" "3.7.2"
  dependencies:
    "memory-fs" "^0.4.1"
    "mime" "^2.4.4"
    "mkdirp" "^0.5.1"
    "range-parser" "^1.2.1"
    "webpack-log" "^2.0.0"

"webpack-dev-server@^3.4.1":
  "integrity" "sha1-J8O10Pa2Z3xDBEZayBdiPIsnuJw="
  "resolved" "https://registry.npm.taobao.org/webpack-dev-server/download/webpack-dev-server-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "ansi-html" "0.0.7"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.8"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "debug" "^4.1.1"
    "del" "^4.1.1"
    "express" "^4.17.1"
    "html-entities" "^1.2.1"
    "http-proxy-middleware" "0.19.1"
    "import-local" "^2.0.0"
    "internal-ip" "^4.3.0"
    "ip" "^1.1.5"
    "is-absolute-url" "^3.0.3"
    "killable" "^1.0.1"
    "loglevel" "^1.6.4"
    "opn" "^5.5.0"
    "p-retry" "^3.0.1"
    "portfinder" "^1.0.25"
    "schema-utils" "^1.0.0"
    "selfsigned" "^1.10.7"
    "semver" "^6.3.0"
    "serve-index" "^1.9.1"
    "sockjs" "0.3.19"
    "sockjs-client" "1.4.0"
    "spdy" "^4.0.1"
    "strip-ansi" "^3.0.1"
    "supports-color" "^6.1.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^3.7.2"
    "webpack-log" "^2.0.0"
    "ws" "^6.2.1"
    "yargs" "12.0.5"

"webpack-log@^2.0.0":
  "integrity" "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8="
  "resolved" "https://registry.npm.taobao.org/webpack-log/download/webpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "^3.0.0"
    "uuid" "^3.3.2"

"webpack-merge@^4.2.1":
  "integrity" "sha1-onxS6ng9E5iv0gh/VH17nS9DY00="
  "resolved" "https://registry.npm.taobao.org/webpack-merge/download/webpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.1.0", "webpack-sources@^1.4.0", "webpack-sources@^1.4.1":
  "integrity" "sha1-7t2OwLko+/HL/plOItLYkPMwqTM="
  "resolved" "https://registry.npm.taobao.org/webpack-sources/download/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.3.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.0.0", "webpack@^4.0.0", "webpack@^4.1.0 || ^5.0.0-0", "webpack@^4.39.3", "webpack@^4.4.0", "webpack@>=2", "webpack@>=2.0.0 <5.0.0", "webpack@>=4.0.0":
  "integrity" "sha1-w07Hbao6hGjJthpQM22OMwPc504="
  "resolved" "https://registry.npm.taobao.org/webpack/download/webpack-4.41.2.tgz"
  "version" "4.41.2"
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-module-context" "1.8.5"
    "@webassemblyjs/wasm-edit" "1.8.5"
    "@webassemblyjs/wasm-parser" "1.8.5"
    "acorn" "^6.2.1"
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^4.1.0"
    "eslint-scope" "^4.0.3"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^2.4.0"
    "loader-utils" "^1.2.3"
    "memory-fs" "^0.4.1"
    "micromatch" "^3.1.10"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.1"
    "node-libs-browser" "^2.2.1"
    "schema-utils" "^1.0.0"
    "tapable" "^1.1.3"
    "terser-webpack-plugin" "^1.4.1"
    "watchpack" "^1.6.0"
    "webpack-sources" "^1.4.1"

"websocket-driver@>=0.5.1":
  "integrity" "sha1-otTg1PTxFvHmKX66WLBdQwEA6fk="
  "resolved" "https://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.7.3.tgz"
  "version" "0.7.3"
  dependencies:
    "http-parser-js" ">=0.4.0 <0.4.11"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha1-XS/yKXcAPsaHpLhwc9+7rBRszyk="
  "resolved" "https://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.3.tgz"
  "version" "0.1.3"

"whatwg-encoding@^1.0.1", "whatwg-encoding@^1.0.3":
  "integrity" "sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA="
  "resolved" "https://registry.npm.taobao.org/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-mimetype@^2.1.0", "whatwg-mimetype@^2.2.0":
  "integrity" "sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78="
  "resolved" "https://registry.npm.taobao.org/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^6.4.1":
  "integrity" "sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag="
  "resolved" "https://registry.npm.taobao.org/whatwg-url/download/whatwg-url-6.5.0.tgz?cache=0&sync_timestamp=1571674002275&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "lodash.sortby" "^4.7.0"
    "tr46" "^1.0.1"
    "webidl-conversions" "^4.0.2"

"whatwg-url@^7.0.0":
  "integrity" "sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY="
  "resolved" "https://registry.npm.taobao.org/whatwg-url/download/whatwg-url-7.1.0.tgz?cache=0&sync_timestamp=1571674002275&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "lodash.sortby" "^4.7.0"
    "tr46" "^1.0.1"
    "webidl-conversions" "^4.0.2"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.12", "which@^1.2.9", "which@^1.3.0":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "https://registry.npm.taobao.org/which/download/which-1.3.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.npm.taobao.org/which/download/which-2.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@~1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "https://registry.npm.taobao.org/word-wrap/download/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wordwrap@~0.0.2":
  "integrity" "sha1-o9XabNXAvAAI03I0u68b7WMFkQc="
  "resolved" "https://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.3.tgz"
  "version" "0.0.3"

"workbox-background-sync@^3.6.3":
  "integrity" "sha1-Zgmg+sntozanxS5qoie6KuUyrZQ="
  "resolved" "https://registry.npm.taobao.org/workbox-background-sync/download/workbox-background-sync-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-broadcast-cache-update@^3.6.3":
  "integrity" "sha1-P13/Iq2oyT45f7OMHcEAYGp7kto="
  "resolved" "https://registry.npm.taobao.org/workbox-broadcast-cache-update/download/workbox-broadcast-cache-update-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-build@^3.6.3":
  "integrity" "sha1-dxEPn1LcXYL6bBw4TG9eIiWty9g="
  "resolved" "https://registry.npm.taobao.org/workbox-build/download/workbox-build-3.6.3.tgz?cache=0&sync_timestamp=1574706418345&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fworkbox-build%2Fdownload%2Fworkbox-build-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "babel-runtime" "^6.26.0"
    "common-tags" "^1.4.0"
    "fs-extra" "^4.0.2"
    "glob" "^7.1.2"
    "joi" "^11.1.1"
    "lodash.template" "^4.4.0"
    "pretty-bytes" "^4.0.2"
    "stringify-object" "^3.2.2"
    "strip-comments" "^1.0.2"
    "workbox-background-sync" "^3.6.3"
    "workbox-broadcast-cache-update" "^3.6.3"
    "workbox-cache-expiration" "^3.6.3"
    "workbox-cacheable-response" "^3.6.3"
    "workbox-core" "^3.6.3"
    "workbox-google-analytics" "^3.6.3"
    "workbox-navigation-preload" "^3.6.3"
    "workbox-precaching" "^3.6.3"
    "workbox-range-requests" "^3.6.3"
    "workbox-routing" "^3.6.3"
    "workbox-strategies" "^3.6.3"
    "workbox-streams" "^3.6.3"
    "workbox-sw" "^3.6.3"

"workbox-cache-expiration@^3.6.3":
  "integrity" "sha1-SBlpclSnIJihP5S1lDJaKKHpA3I="
  "resolved" "https://registry.npm.taobao.org/workbox-cache-expiration/download/workbox-cache-expiration-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-cacheable-response@^3.6.3":
  "integrity" "sha1-hp8aaPzpBj9oad2/f6Ci4Khos6o="
  "resolved" "https://registry.npm.taobao.org/workbox-cacheable-response/download/workbox-cacheable-response-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-core@^3.6.3":
  "integrity" "sha1-aau6cKTz8qXAWSlabzt8Yr0A4Vw="
  "resolved" "https://registry.npm.taobao.org/workbox-core/download/workbox-core-3.6.3.tgz"
  "version" "3.6.3"

"workbox-google-analytics@^3.6.3":
  "integrity" "sha1-md8qPXDW6Rlh4YpnUrrBLpH79yc="
  "resolved" "https://registry.npm.taobao.org/workbox-google-analytics/download/workbox-google-analytics-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-background-sync" "^3.6.3"
    "workbox-core" "^3.6.3"
    "workbox-routing" "^3.6.3"
    "workbox-strategies" "^3.6.3"

"workbox-navigation-preload@^3.6.3":
  "integrity" "sha1-osNOt8F+dIW3lRJQkSFfdXs8SWQ="
  "resolved" "https://registry.npm.taobao.org/workbox-navigation-preload/download/workbox-navigation-preload-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-precaching@^3.6.3":
  "integrity" "sha1-U0FRXp1YcsWO3gJqMeGbr6+k4cE="
  "resolved" "https://registry.npm.taobao.org/workbox-precaching/download/workbox-precaching-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-range-requests@^3.6.3":
  "integrity" "sha1-PMIcujHy3YxDxSoZa8yPbNvN6AM="
  "resolved" "https://registry.npm.taobao.org/workbox-range-requests/download/workbox-range-requests-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-routing@^3.6.3":
  "integrity" "sha1-ZZzY+SdJhs+pj9oNBQ3mQiB1rPc="
  "resolved" "https://registry.npm.taobao.org/workbox-routing/download/workbox-routing-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-strategies@^3.6.3":
  "integrity" "sha1-EaDcJJp7wj00ZewTItKPpmQ9ZKA="
  "resolved" "https://registry.npm.taobao.org/workbox-strategies/download/workbox-strategies-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-streams@^3.6.3":
  "integrity" "sha1-vq6l1bIwI5g2zDJ7B9RxqmEBlVo="
  "resolved" "https://registry.npm.taobao.org/workbox-streams/download/workbox-streams-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "^3.6.3"

"workbox-sw@^3.6.3":
  "integrity" "sha1-J46kwYMbkrvi1CDag5kXbEsnif8="
  "resolved" "https://registry.npm.taobao.org/workbox-sw/download/workbox-sw-3.6.3.tgz"
  "version" "3.6.3"

"workbox-webpack-plugin@^3.6.3":
  "integrity" "sha1-qAe7iRtOTjyAjfB+WPF94tW6YYI="
  "resolved" "https://registry.npm.taobao.org/workbox-webpack-plugin/download/workbox-webpack-plugin-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "babel-runtime" "^6.26.0"
    "json-stable-stringify" "^1.0.1"
    "workbox-build" "^3.6.3"

"worker-farm@^1.7.0":
  "integrity" "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag="
  "resolved" "https://registry.npm.taobao.org/worker-farm/download/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"wrap-ansi@^2.0.0":
  "integrity" "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU="
  "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-2.1.0.tgz?cache=0&sync_timestamp=1573488536792&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"

"wrap-ansi@^5.1.0":
  "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
  "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-5.1.0.tgz?cache=0&sync_timestamp=1573488536792&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM="
  "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-6.2.0.tgz?cache=0&sync_timestamp=1573488536792&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^2.1.0":
  "integrity" "sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE="
  "resolved" "https://registry.npm.taobao.org/write-file-atomic/download/write-file-atomic-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "graceful-fs" "^4.1.11"
    "imurmurhash" "^0.1.4"
    "signal-exit" "^3.0.2"

"write-file-atomic@2.4.1":
  "integrity" "sha1-0LBUY8GIroBDlv1asqNwBir4dSk="
  "resolved" "https://registry.npm.taobao.org/write-file-atomic/download/write-file-atomic-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "imurmurhash" "^0.1.4"
    "signal-exit" "^3.0.2"

"write@^0.2.1":
  "integrity" "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c="
  "resolved" "https://registry.npm.taobao.org/write/download/write-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "mkdirp" "^0.5.1"

"write@1.0.3":
  "integrity" "sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM="
  "resolved" "https://registry.npm.taobao.org/write/download/write-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "mkdirp" "^0.5.1"

"ws@^5.2.0":
  "integrity" "sha1-3/7xSGa46NyRM1glFNG++vlumA8="
  "resolved" "https://registry.npm.taobao.org/ws/download/ws-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@^6.0.0":
  "integrity" "sha1-RC/fCkftZPWbal2P8TD0dI7VJPs="
  "resolved" "https://registry.npm.taobao.org/ws/download/ws-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@^6.2.1":
  "integrity" "sha1-RC/fCkftZPWbal2P8TD0dI7VJPs="
  "resolved" "https://registry.npm.taobao.org/ws/download/ws-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "async-limiter" "~1.0.0"

"xml-name-validator@^3.0.0":
  "integrity" "sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo="
  "resolved" "https://registry.npm.taobao.org/xml-name-validator/download/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
  "resolved" "https://registry.npm.taobao.org/xtend/download/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^3.2.1 || ^4.0.0", "y18n@^4.0.0":
  "integrity" "sha1-le+U+F7MgdAHwmThkKEg8KPIVms="
  "resolved" "https://registry.npm.taobao.org/y18n/download/y18n-4.0.0.tgz"
  "version" "4.0.0"

"y18n@^3.2.1":
  "integrity" "sha1-bRX7qITAhnnA136I53WegR4H+kE="
  "resolved" "https://registry.npm.taobao.org/y18n/download/y18n-3.2.1.tgz"
  "version" "3.2.1"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz?cache=0&sync_timestamp=1569874170379&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyallist%2Fdownload%2Fyallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-3.1.1.tgz?cache=0&sync_timestamp=1569874170379&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyallist%2Fdownload%2Fyallist-3.1.1.tgz"
  "version" "3.1.1"

"yargs-parser@^11.1.1":
  "integrity" "sha1-h5oIZZc7yp9rq1y987HGfsfTvPQ="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-11.1.1.tgz"
  "version" "11.1.1"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^13.1.1":
  "integrity" "sha1-0mBYUyqgbTZf4JH2ofwGsvfl7KA="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.1.1.tgz"
  "version" "13.1.1"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^16.1.0":
  "integrity" "sha1-c3R9U64YfnuNvjM/lXFMduoA7PE="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-16.1.0.tgz"
  "version" "16.1.0"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^9.0.2":
  "integrity" "sha1-nM9qQ0YP5O1Aqbto9I1DuKaMwHc="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "camelcase" "^4.1.0"

"yargs-parser@10.x":
  "integrity" "sha1-cgImW4n36eny5XZeD+c1qQXtuqg="
  "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "camelcase" "^4.1.0"

"yargs@^11.0.0":
  "integrity" "sha1-UFLv40RqTfXtZpyZWIbMDxNwJ2Y="
  "resolved" "https://registry.npm.taobao.org/yargs/download/yargs-11.1.1.tgz"
  "version" "11.1.1"
  dependencies:
    "cliui" "^4.0.0"
    "decamelize" "^1.1.1"
    "find-up" "^2.1.0"
    "get-caller-file" "^1.0.1"
    "os-locale" "^3.1.0"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^2.0.0"
    "which-module" "^2.0.0"
    "y18n" "^3.2.1"
    "yargs-parser" "^9.0.2"

"yargs@^12.0.1", "yargs@12.0.5":
  "integrity" "sha1-BfWZe2CWR7ZPZrgeO0sQo2jnrRM="
  "resolved" "https://registry.npm.taobao.org/yargs/download/yargs-12.0.5.tgz"
  "version" "12.0.5"
  dependencies:
    "cliui" "^4.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^1.0.1"
    "os-locale" "^3.0.0"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^2.0.0"
    "which-module" "^2.0.0"
    "y18n" "^3.2.1 || ^4.0.0"
    "yargs-parser" "^11.1.1"

"yargs@^13.3.0":
  "integrity" "sha1-TGV6VeB+Xyz5R/ijZlZ8BKDe3IM="
  "resolved" "https://registry.npm.taobao.org/yargs/download/yargs-13.3.0.tgz"
  "version" "13.3.0"
  dependencies:
    "cliui" "^5.0.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^13.1.1"

"yargs@^15.0.0":
  "integrity" "sha1-Qki/IY7wUDhcT34U699CVlPRO9M="
  "resolved" "https://registry.npm.taobao.org/yargs/download/yargs-15.0.2.tgz"
  "version" "15.0.2"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^16.1.0"

"yauzl@2.10.0":
  "integrity" "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk="
  "resolved" "https://registry.npm.taobao.org/yauzl/download/yauzl-2.10.0.tgz"
  "version" "2.10.0"
  dependencies:
    "buffer-crc32" "~0.2.3"
    "fd-slicer" "~1.1.0"

"yauzl@2.4.1":
  "integrity" "sha1-lSj0QtqxsihOWLQ3m7GU4i4MQAU="
  "resolved" "https://registry.npm.taobao.org/yauzl/download/yauzl-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "fd-slicer" "~1.0.1"

"yorkie@^2.0.0":
  "integrity" "sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k="
  "resolved" "https://registry.npm.taobao.org/yorkie/download/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"

"zrender@5.3.1":
  "integrity" "sha512-7olqIjy0gWfznKr6vgfnGBk7y4UtdMvdwFmK92vVQsQeDPyzkHW1OlrLEKg6GHz1W5ePf0FeN1q2vkl/HFqhXw=="
  "resolved" "https://registry.npmjs.org/zrender/-/zrender-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "tslib" "2.3.0"
