# 权限控制刷新问题修复说明

## 🔧 修复内容

### 问题原因
页面刷新时，Vuex store 中的用户信息被清空，导致权限检查失败，菜单项消失。

### 解决方案

#### 1. **Store 初始化修复** (`src/store/modules/user.ts`)
- 在 store 初始化时自动从 cookies 恢复用户信息
- 添加了 `initUserInfo()` 方法和 `SetUserInfo()` 公共方法

#### 2. **权限工具函数增强** (`src/utils/permission.ts`)
- 增加了多重用户信息获取策略
- 添加了从 cookies 直接解析的备用方案
- 提高了用户信息获取的可靠性

#### 3. **路由守卫优化** (`src/router.ts`)
- 添加了 `ensureUserInfoLoaded()` 函数
- 在权限检查前确保用户信息已加载
- 支持异步用户信息恢复

#### 4. **侧边栏响应式更新** (`src/layout/components/Sidebar/index.vue`)
- 添加了对 UserModule.userInfo 的响应式依赖
- 确保用户信息变化时菜单自动更新

## 🧪 测试步骤

### 测试1：管理员用户（ID=1）
1. 使用商户ID为1的账号登录
2. 验证能看到所有菜单项（工作台、数据统计、订单管理、商户管理）
3. **刷新页面**
4. 验证菜单项依然显示正常

### 测试2：普通商户用户（ID≠1）
1. 使用其他商户ID的账号登录
2. 验证看不到受限菜单项
3. **刷新页面**
4. 验证受限菜单项依然不显示

### 测试3：直接URL访问
1. 非管理员用户直接访问 `/dashboard`、`/statistics`、`/order`、`/employee`
2. 验证会被重定向到 `/dish` 页面

## 🔍 调试信息

在浏览器控制台中可以看到以下调试信息：
- `当前用户信息:` - 显示当前加载的用户信息
- `过滤后的菜单:` - 显示权限过滤后的菜单列表
- `权限不足，重定向到商品管理页面` - 当非管理员访问受限页面时

## 📝 关键改进

1. **多重保障**：从 store → cookies → 直接解析 cookies 的三重获取策略
2. **异步加载**：路由守卫支持异步等待用户信息加载完成
3. **响应式更新**：侧边栏菜单会响应用户信息的变化
4. **错误处理**：完善的错误处理和降级机制

## ⚠️ 注意事项

- 修复后的权限控制更加可靠，但仍建议后端API也要有相应的权限验证
- 如果用户信息结构发生变化，可能需要相应调整权限检查逻辑
- 建议在生产环境中关闭调试日志输出

## 🎯 预期效果

修复后，无论是否刷新页面，权限控制都应该正常工作：
- 管理员用户始终能看到所有菜单
- 普通商户用户始终看不到受限菜单
- 直接URL访问受限页面会被正确拦截
